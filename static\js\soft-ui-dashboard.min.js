"use strict";function getEventTarget(e){return e=e||window.event,e.target||e.srcElement}function sidebarColor(e){for(var t=e.parentElement.children,n=e.getAttribute("data-color"),i=0;i<t.length;i++)t[i].classList.remove("active");e.classList.contains("active")?e.classList.remove("active"):e.classList.add("active");var a=document.querySelector(".sidenav");if(a.setAttribute("data-color",n),document.querySelector("#sidenavCard")){var s=document.querySelector("#sidenavCard");let e=["card","card-background","shadow-none","card-background-mask-"+n];s.className="",s.classList.add(...e);var l=document.querySelector("#sidenavCardIcon");let t=["ni","ni-diamond","text-gradient","text-lg","top-0","text-"+n];l.className="",l.classList.add(...t)}}function navbarFixed(e){let t=["position-sticky","blur","shadow-blur","mt-4","left-auto","top-1","z-index-sticky"];const n=document.getElementById("navbarBlur");e.getAttribute("checked")?(n.classList.remove(...t),n.setAttribute("navbar-scroll","false"),navbarBlurOnScroll("navbarBlur"),e.removeAttribute("checked")):(n.classList.add(...t),n.setAttribute("navbar-scroll","true"),navbarBlurOnScroll("navbarBlur"),e.setAttribute("checked","true"))}function navbarBlurOnScroll(e){function t(){a.classList.add(...o),a.classList.remove(...r),i("blur")}function n(){a&&(a.classList.remove(...o),a.classList.add(...r),i("transparent"))}function i(e){let t=document.querySelectorAll(".navbar-main .nav-link"),n=document.querySelectorAll(".navbar-main .sidenav-toggler-line");"blur"===e?(t.forEach(e=>{e.classList.remove("text-body")}),n.forEach(e=>{e.classList.add("bg-dark")})):"transparent"===e&&(t.forEach(e=>{e.classList.add("text-body")}),n.forEach(e=>{e.classList.remove("bg-dark")}))}const a=document.getElementById(e);let s=!!a&&a.getAttribute("navbar-scroll"),l=5,o=["position-sticky","blur","shadow-blur","mt-4","left-auto","top-1","z-index-sticky"],r=["shadow-none"];window.onscroll=debounce("true"==s?function(){window.scrollY>l?t():n()}:function(){n()},10)}function debounce(e,t,n){var i;return function(){var a=this,s=arguments,l=function(){i=null,n||e.apply(a,s)},o=n&&!i;clearTimeout(i),i=setTimeout(l,t),o&&e.apply(a,s)}}function sidebarType(e){for(var t=e.parentElement.children,n=e.getAttribute("data-class"),i=[],a=0;a<t.length;a++)t[a].classList.remove("active"),i.push(t[a].getAttribute("data-class"));e.classList.contains("active")?e.classList.remove("active"):e.classList.add("active");var s=document.querySelector(".sidenav");for(a=0;a<i.length;a++)s.classList.remove(i[a]);s.classList.add(n)}function toggleSidenav(){body.classList.contains(className)?(body.classList.remove(className),setTimeout(function(){sidenav.classList.remove("bg-white")},100),sidenav.classList.remove("bg-transparent")):(body.classList.add(className),sidenav.classList.add("bg-white"),sidenav.classList.remove("bg-transparent"),iconSidenav.classList.remove("d-none"))}function navbarColorOnResize(){window.innerWidth>1200?referenceButtons.classList.contains("active")&&"bg-transparent"===referenceButtons.getAttribute("data-class")?sidenav.classList.remove("bg-white"):sidenav.classList.add("bg-white"):(sidenav.classList.add("bg-white"),sidenav.classList.remove("bg-transparent"))}function sidenavTypeOnResize(){let e=document.querySelectorAll('[onclick="sidebarType(this)"]');window.innerWidth<1200?e.forEach(function(e){e.classList.add("disabled")}):e.forEach(function(e){e.classList.remove("disabled")})}navbarBlurOnScroll("navbarBlur");var tooltipTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')),tooltipList=tooltipTriggerList.map(function(e){return new bootstrap.Tooltip(e)});if(document.querySelector(".fixed-plugin")){var fixedPlugin=document.querySelector(".fixed-plugin"),fixedPluginButton=document.querySelector(".fixed-plugin-button"),fixedPluginButtonNav=document.querySelector(".fixed-plugin-button-nav"),fixedPluginCard=document.querySelector(".fixed-plugin .card"),fixedPluginCloseButton=document.querySelectorAll(".fixed-plugin-close-button"),navbar=document.getElementById("navbarBlur"),buttonNavbarFixed=document.getElementById("navbarFixed");fixedPluginButton&&(fixedPluginButton.onclick=function(){fixedPlugin.classList.contains("show")?fixedPlugin.classList.remove("show"):fixedPlugin.classList.add("show")}),fixedPluginButtonNav&&(fixedPluginButtonNav.onclick=function(){fixedPlugin.classList.contains("show")?fixedPlugin.classList.remove("show"):fixedPlugin.classList.add("show")}),fixedPluginCloseButton.forEach(function(e){e.onclick=function(){fixedPlugin.classList.remove("show")}}),document.querySelector("body").onclick=function(e){e.target!=fixedPluginButton&&e.target!=fixedPluginButtonNav&&e.target.closest(".fixed-plugin .card")!=fixedPluginCard&&fixedPlugin.classList.remove("show")},navbar&&"true"==navbar.getAttribute("navbar-scroll")&&buttonNavbarFixed.setAttribute("checked","true")}var total=document.querySelectorAll(".nav-pills");total.forEach(function(e,t){var n=document.createElement("div"),i=e.querySelector("li:first-child .nav-link"),a=i.cloneNode();a.innerHTML="-",n.classList.add("moving-tab","position-absolute","nav-link"),n.appendChild(a),e.appendChild(n);e.getElementsByTagName("li").length;n.style.padding="0px",n.style.width=e.querySelector("li:nth-child(1)").offsetWidth+"px",n.style.transform="translate3d(0px, 0px, 0px)",n.style.transition=".5s ease",e.onmouseover=function(t){let i=getEventTarget(t),a=i.closest("li");if(a){let t=Array.from(a.closest("ul").children),i=t.indexOf(a)+1;e.querySelector("li:nth-child("+i+") .nav-link").onclick=function(){n=e.querySelector(".moving-tab");let s=0;if(e.classList.contains("flex-column")){for(var l=1;l<=t.indexOf(a);l++)s+=e.querySelector("li:nth-child("+l+")").offsetHeight;n.style.transform="translate3d(0px,"+s+"px, 0px)",n.style.height=e.querySelector("li:nth-child("+l+")").offsetHeight}else{for(l=1;l<=t.indexOf(a);l++)s+=e.querySelector("li:nth-child("+l+")").offsetWidth;n.style.transform="translate3d("+s+"px, 0px, 0px)",n.style.width=e.querySelector("li:nth-child("+i+")").offsetWidth+"px"}}}}}),window.addEventListener("resize",function(e){total.forEach(function(e,t){e.querySelector(".moving-tab").remove();var n=document.createElement("div"),i=e.querySelector(".nav-link.active").cloneNode();i.innerHTML="-",n.classList.add("moving-tab","position-absolute","nav-link"),n.appendChild(i),e.appendChild(n),n.style.padding="0px",n.style.transition=".5s ease";let a=e.querySelector(".nav-link.active").parentElement;if(a){let t=Array.from(a.closest("ul").children),i=t.indexOf(a)+1,l=0;if(e.classList.contains("flex-column")){for(var s=1;s<=t.indexOf(a);s++)l+=e.querySelector("li:nth-child("+s+")").offsetHeight;n.style.transform="translate3d(0px,"+l+"px, 0px)",n.style.width=e.querySelector("li:nth-child("+i+")").offsetWidth+"px",n.style.height=e.querySelector("li:nth-child("+s+")").offsetHeight}else{for(s=1;s<=t.indexOf(a);s++)l+=e.querySelector("li:nth-child("+s+")").offsetWidth;n.style.transform="translate3d("+l+"px, 0px, 0px)",n.style.width=e.querySelector("li:nth-child("+i+")").offsetWidth+"px"}}}),window.innerWidth<991?total.forEach(function(e,t){e.classList.contains("flex-column")||e.classList.add("flex-column","on-resize")}):total.forEach(function(e,t){e.classList.contains("on-resize")&&e.classList.remove("flex-column","on-resize")})});const iconNavbarSidenav=document.getElementById("iconNavbarSidenav"),iconSidenav=document.getElementById("iconSidenav"),sidenav=document.getElementById("sidenav-main");let body=document.getElementsByTagName("body")[0],className="g-sidenav-pinned";iconNavbarSidenav&&iconNavbarSidenav.addEventListener("click",toggleSidenav),iconSidenav&&iconSidenav.addEventListener("click",toggleSidenav);let referenceButtons=document.querySelector("[data-class]");window.addEventListener("resize",navbarColorOnResize),window.addEventListener("resize",sidenavTypeOnResize),window.addEventListener("load",sidenavTypeOnResize);