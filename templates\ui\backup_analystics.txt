<!DOCTYPE html>
<html lang="en">
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BPlan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"/>

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/all.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-duotone-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-thin.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-regular.css">
    <link rel="icon" type="image/x-icon" href="/static/img/favicon.png">
    <link rel="stylesheet" href="/static/css/reb.css">
    <script src="
    https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.all.min.js
    "></script>
    <link href="
    https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.min.css
    " rel="stylesheet">
    <link rel="stylesheet" href="/static/nrc/analystics_style.css">
</head>
<script >  
    function e(){window.scrollTo(0,0)}
</script>
<body style="overflow-y: hidden;">

    <div class="background"></div>
    <div class="container">
        <div class="intro " style="height: 100%;">
            <div class="mainform" id="mdxform">
                <div class="suform">
                    <select class="form-select" name="room_choose" style="
                    padding: 4px;
                " id="room_choose">
                    </select>
                    
                    <!-- <label for="room_choose" class="appear intro">{{ language["bpl.report_choose"] }}</label> -->
                    
                </div>


            </div>
            <h6 class="appear intro" id="title">
                <label id="currtime"></label>
                <input type="date" id="time_choose" style="width: 30%;padding: 3px;font-size: small;border-radius: 0.375rem;">
                <input type="date" id="time_choose2" style="width: 30%;padding: 3px;font-size: small;border-radius: 0.375rem;">
                <button type="button" id="excel_id" style="width: 20%;padding: 2px;white-space: nowrap;font-size: small;" class="btn btn-light">{{language["bpl.export_excel"]}}</button>
                <button type="button" id="show_id" style="width: 15%;padding: 2px;white-space: nowrap;font-size: small;" class="btn btn-light"><i class="fa-solid fa-eye"></i></button>
                <button type="button" id="back_id" style="width: 15%;padding: 2px;white-space: nowrap;font-size: small;" class="btn btn-light"><i class="fa-solid fa-left"></i></button>
            </h6>

            <hr>

            
            <div class="flexible" id="student_choose" style="height: 100%;width: 100%;">
                <table border="1" class="rwd-table" id="studentboard_mt" style="
                flex: 0 0 100%;
                height: 100%;
                width: 100%;
            ">
            <thead>

                <tr>
                    <th>{{language["bpl.table.group_name"]}}</th>
                    <th>{{language["bpl.table.online"]}}</th>
                    <th>%</th>
                    <th>{{language["bpl.offline_wr"]}}</th>
                    <th>{{language["bpl.offline_wtr"]}}</th>
                    <th>{{language["bpl.table.online_boarding"]}}</th>
                    <th>%</th>
                    <th>{{language["bpl.offline_wr"]}}</th>
                    <th>{{language["bpl.offline_wtr"]}}</th>
                </tr>
            </thead>
            <tbody id="studentboard">
                
            </tbody>

                      </table>
                      <h3>{{language["bpl.list_pu"]}} 
                        <button type="button" id="excel_id2" style="width: 25%;padding: 2px;white-space: nowrap;font-size: small;" class="btn btn-light">{{language["bpl.downloadfile"]}}</button>
                      </h3>
                      <table border="1" class="specialiu rwd-table tablesorter-blue" id="studentmain" style="
                      flex: 0 0 100%;
                      height: 100%;
                      width: 100%;
                  ">
                            <thead>

                                <tr>
                                    <th>{{language["bpl.table.group_name"]}}</th>
                                    <th>{{language["bpl.student-nm"]}}</th>
                                    <th>{{language["bpl.place"]}}</th>
                                    <th>{{language["bpl.table.online_boarding"]}}</th>
                                    <th>{{language["bpl.table.room"]}}</th>
                                    <th>{{language["bpl.father_name"]}}</th>
                                    <th>{{language["bpl.mother_name"]}}</th>
                                    <th>{{language["bpl.father_number"]}}</th>
                                    <th>{{language["bpl.mother_number"]}}</th>
                                    <th>{{language["bpl.offline_wr"]}}</th>
                                    <th>{{language["bpl.offline_wtr"]}}</th>
                                </tr>
                            </thead>
                            <tbody id="studentmain_mini">

                            </tbody>
                    
                        </table>
                        <center>

                            <small style="color: white;">(C) 2025 By HoangDat</small>
                        </center>

                    </div>

                              





                </ul>
            </div>

        </div>
    </div>
</body>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.32.0/js/jquery.tablesorter.min.js" integrity="sha512-O/JP2r8BG27p5NOtVhwqsSokAwEP5RwYgvEzU9G6AfNjLYqyt2QT8jqU1XrXCiezS50Qp1i3ZtCQWkHZIRulGA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.32.0/js/jquery.tablesorter.widgets.min.js" integrity="sha512-80UtjM9ccaZ/RA3+geoQut0j0p5cpnct9Bk65BrCFil/P3r6+1IilzXnkAmvvFnWEldujRqTkjZapo5+rP9cAA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.32.0/css/dragtable.mod.min.css" integrity="sha512-nR2yAQRLPLBoJHLWmvXFWp+bg7tQ8Vl6AwxE5HgCMLQ3eRUQk0l4d/CmW4F4YOzUiNRgnlLLDGrutDOzy1ImIA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.32.0/css/theme.blue.min.css" integrity="sha512-jJ9r3lTLaH5XXa9ZOsCQU8kLvxdAVzyTWO/pnzdZrshJQfnw1oevJFpoyCDr7K1lqt1hUgqoxA5e2PctVtlSTg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<script src="https://cdn.jsdelivr.net/npm/@linways/table-to-excel@1.0.4/dist/tableToExcel.min.js"></script>
<script>
    function setCookie(name,value,days) {
        try {       
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days*24*60*60*1000));
                expires = "; expires=" + date.toUTCString();
            };
            document.cookie = name + "=" + (value || "")  + expires + "; path=/";
        }catch{
            
        }
    };
    function getCookie(name) {
        try{

            var nameEQ = name + "=";
            var ca = document.cookie.split(';');
            for(var i=0;i < ca.length;i++) {
                var c = ca[i];
                while (c.charAt(0)==' ') c = c.substring(1,c.length);
                if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
            };
            return null;
        }catch{
            return null;
        };
    };
    window.currentAdd = false;
    try{

        if (getCookie("rcaching")){
            window.rcaching = JSON.parse(getCookie("rcaching"));
        }else{
            window.rcaching = {};
        };
    }catch{
        window.rcaching = {};
    };
    function exportToExcel() {
        currentdt = $g("time_choose").value;
        (TableToExcel.convert(document.getElementById("studentboard"), {
            name: "BPlan_{{language['bpl.report_text']}}_"+currentdt+".xlsx",
            sheet: {
                name: "Sheet 1"
            }
        }));
    };
    function exportToExcel2() {
        currentdt = $g("time_choose").value;
        (TableToExcel.convert(document.getElementById("studentmain"), {
            name: "BPlan_{{language['bpl.list_text']}}_"+currentdt+".xlsx",
            sheet: {
                name: "Sheet 1"
            }
        }));
    };

    function $g(id) {
        return document.getElementById(id);
    };
    $g("back_id").onclick = function(){
        window.location.href = window.location.origin + `{{url_for('dshb')}}`;
    };
    $g("excel_id").onclick = exportToExcel;
    $g("excel_id2").onclick = exportToExcel2;
    origin = $g("studentboard").innerHTML;
    $("#studentmain").tablesorter();

    $("#studentmain")
    .bind("sortEnd",function(e, table) {
        if (window.currentAdd){
            $("#studentmain_mini .eyeline").remove();
            addPred([],true);
        }
    });
    $.tablesorter.clearTableBody( '#studentmain' );
    // rorigin = $("#studentmain").clearTableBody('tbody');



    ownergr = {{ownergroup | safe}};roomjoin = {{roomjoin | safe}};
    ownergr.forEach(element => {
        data = document.createElement("option");
        data.value = element;
        data.innerHTML = element;
        $g("room_choose").appendChild(data)
    });
    roomjoin.forEach(element => {
        data = document.createElement("option");
        data.value = element;
        data.innerHTML = element;
        $g("room_choose").appendChild(data)
    });
    if (!(roomjoin.length + ownergr.length > 1)){
        $g("mdxform").style.display = "none";
    };
 
    async function getSub(url,caching=false,showloading=false){
        rnotify = null;
        if (showloading){
            rnotify = Swal.fire({
                title: "{{language['wp.waiting']}}",
                html: "{{language['wp.request_inprocess']}}",
                didOpen: () => {
                    Swal.showLoading();
                },
                willClose: () => {

                },
                allowOutsideClick :false
            });
        };
        if (caching == true){
            try{

                if (url in window.rcaching && (!([null,undefined].includes(window.rcaching[url])))){
                    if (rnotify){rnotify.close()};
                    return window.rcaching[url];
                };
            }catch{
            };
        };
        var data;
        await fetch(url)
            .then((response) => {
                return response.json()
            })
            .then((datar) => {
                data = datar;
        });
        window.rcaching[url] = data;
        setCookie("rcaching",JSON.stringify(window.rcaching),7);
        if (rnotify){rnotify.close()};
        return data;
    };
    function addAnalystic(list,isInv=false){
        var node_1 = document.createElement('TR');
        if(isInv){
            node_1.classList = ["eyeline"];
        };
        for (let index = 0; index < list.length; index++) {
            const element = list[index];
            var node_2 = document.createElement('TD');
            node_2.innerHTML = element;
            node_1.appendChild(node_2);      
        };
        $g("studentboard").appendChild(node_1);
    };
    function addPred(list,isInv=false){
        var node_1 = document.createElement('TR');
        if (isInv) {
            node_1.classList = ["eyeline"];
        };
        for (let index = 0; index < list.length; index++) {
            const element = list[index];
            var node_2 = document.createElement('TD');
            node_2.innerHTML = element;
            node_1.appendChild(node_2);
        };
        $g("studentmain_mini").appendChild(node_1);
    };



    function strfm(string,list){
        for (let I = 0; I < list.length; I++) {
            string = string.replace("{"+I+"}",list[I]);
        };
        return string;
    };
    function floorium (data){ 
        if (isNaN(data)){
            data = "0"
        };
        return data;
    };
    function msf(string){
        if (string == "0" || string == 0){
            string = ""
        };
        return string;
    };
    function toDateInputValue(dateObject){
        const local = new Date(dateObject);
        local.setMinutes(dateObject.getMinutes() - dateObject.getTimezoneOffset());
        return local.toJSON().slice(0,10);
    };
    async function core3d(curr,nus="") {
        if (curr == true){
            $g("time_choose").value = null;
            $g("time_choose").value = toDateInputValue(new Date());
        };
        rd = $g("time_choose").value;
        rd2 = $g("time_choose2").value;
        data = await getSub("/report_data?room=" + $g("room_choose").value + "&action=analystic&date="+rd+"&date2="+rd2+nus,false,true);
        $g("studentboard").innerHTML = origin;
        $.tablesorter.clearTableBody( '#studentmain' );
        if (data["output"]==true){
            preditor = {};
            allnum = {
                "%0%all":0,
            };
            allboard = {
                "%0%all":0,
            };
            misnum = {
                "%0%all":0,
                "%0%wr":0,
                "%0%wtr":0,
            };
            misboard = {
                "%0%all":0,
                "%0%wr":0,
                "%0%wtr":0,
            };
            for (const key in data["message"]) {
                dtmsg = data["message"][key];
                console.log(111)
                console.log(dtmsg);
                dout = [];
                key_data = Object.keys(dtmsg).sort();
                key_data.filter(v => !(v == "")).forEach ( class_room => {
                group = class_room;
                group_elem = dtmsg[class_room];
                if (!(group in allnum)){
                    allnum[group] = 0;
                };
                if (!(group in misnum)){
                    misnum[group] = {"%0%wr":0,"%0%wtr":0,'%0%all':0};
                };
                if (!(group in allboard)){
                    allboard[group] = 0;
                };
                if (!(group in misboard)){
                    misboard[group] = {"%0%wr":0,"%0%wtr":0,'%0%all':0};
                };
                delete group_elem["reportedBy"];
                delete group_elem["reportedTime"];
                for (const edd in group_elem) {
                    const element = group_elem[edd];
                    if (['Có','1',"BT"].includes(element['boarding'])){
                        boarder = true ;  
                    }else{
                        boarder = false;
                    };
                    if (boarder){
                        allboard[group] += 1;
                        allboard['%0%all'] += 1;
                    };
                    allnum[group] += 1;
                    allnum['%0%all'] += 1;
                    if (element['status'].includes("offline")){
                        if (!(group in preditor)){
                            preditor[group] = {};
                            dout.push(group);
                        };

                        misnum['%0%all'] += 1;
                        misnum[group]['%0%all'] += 1;
                        if (boarder) {
                            misboard[group]["%0%all"] += 1;
                            misboard['%0%all'] += 1;
                        };
                        if (element['status'] == "offline_wtr"){
                            misnum[group]["%0%wtr"] += 1;
                            misnum['%0%wtr'] += 1;
                            if (boarder){
                                misboard[group]["%0%wtr"] += 1;
                                misboard['%0%wtr'] += 1;
                            };    
                        } else if (element['status'] == "offline_wr"){
                            misnum[group]["%0%wr"] += 1;
                            misnum['%0%wr'] += 1;                       
                            if (boarder){
                                misboard[group]["%0%wr"] += 1;
                                misboard['%0%wr'] += 1;
                            };
                        };
                    };
                };
            });
            dout = dout.join(",");
            console.log(dout)
            dout = await getSub("/report_data?action=getData&target="+dout+"&room="+$g("room_choose").value,true);
            dout = dout["message"]; 
            for ( group in dtmsg) {
                if (group == ""){
                    continue;
                };
                group_elem = dtmsg[group];
                for (const edd in group_elem) {
                    const element = group_elem[edd];
                    console.log(edd,group_elem,element);
                    if (element['status'].includes("offline")){
                        if (!(group in preditor)){
                            preditor[group] = {};
                        };
                        if (!(edd in preditor[group])){
                            preditor[group][edd] = {"name":element["name"],"status":element['status'],"place":dout[group][edd]["place"],"father_number":dout[group][edd]["father_number"],"mother_number":dout[group][edd]["mother_number"],"boarding":dout[group][edd]["boarding"],"mother_name":dout[group][edd]["mother_name"],"father_name":dout[group][edd]["father_name"],"boarding_room":dout[group][edd]["boarding_room"],times:1};
                        }else{
                            preditor[group][edd].times += 1;
                        };
                        
                    };
                };
            };      

            for (const key in preditor) {
                const element = preditor[key];
                for (const ekk in element) {
                    const nim = element[ekk];
                    gir = [];
                    if (nim["status"] == "offline_wr") {
                        gir = ["x",""];
                    }else{
                        gir = ["","x"];
                    };
                    // addPred([key,nim["name"],gir[0],gir[1],nim["place"],nim["boarding"],nim["father_number"],nim["mother_number"]])
                    addPred([key, nim["name"], nim["place"], nim["boarding"],nim["boarding_room"], nim["father_name"], nim["mother_name"],nim["father_number"],
                    nim["mother_number"],gir[0], gir[1]
                    ]);
                };
            };

            for (const key in allnum) {
                const element = allnum[key];
                if (!(key.includes('%0%'))) {
                    addAnalystic(
                        [key,
                        
                        strfm("{0}/{1}",
                            [
                            (allnum[key]-misnum[key]['%0%all']),allnum[key]
                            ]),
                        floorium(Math.floor((element-misnum[key]['%0%all'])/element*100))+"%",
                        msf(misnum[key]['%0%wr']),
                        msf(misnum[key]['%0%wtr']),
                        strfm("{0}/{1}",
                            [
                            (allboard[key]-misboard[key]['%0%all']),allboard[key]
                            ]),
                        floorium(Math.floor((allboard[key]-misboard[key]['%0%all'])/allboard[key]*100))+"%",
                        msf(misboard[key]['%0%wr']),
                        msf(misboard[key]['%0%wtr'])
                    ]);
                };
            };
            addAnalystic(
                        ["{{language['bpl.sum']}}",
                        
                        strfm("{0}/{1}",
                            [
                            (allnum["%0%all"]-misnum['%0%all']),allnum["%0%all"]
                            ]),
                        floorium(Math.floor((allnum["%0%all"]-misnum['%0%all'])/allnum["%0%all"]*100))+"%",
                        msf(misnum['%0%wr']),
                        msf(misnum['%0%wtr']),
                        strfm("{0}/{1}",
                            [
                            (allboard["%0%all"]-misboard['%0%all']),allboard["%0%all"]
                            ]),
                        floorium(Math.floor((allboard["%0%all"]-misboard['%0%all'])/allboard["%0%all"]*100))+"%",
                        msf(misboard['%0%wr']),
                        msf(misboard['%0%wtr'])
                    ]);
 
                        
                }
            }else{
                    if (data["message"] == "No marker time"){
                        Swal.fire({
                            title: '{{language["bpl.chooseMarker"]}}', 
                            html: `<select id="marker_list" class="swal2-select" style="margin: 5px;display: flex;"></select>`,  
                            confirmButtonText: "Ok", 
                            showCancelButton: true,
                            preConfirm: async () => {
                                nus = "&marker="+$g("marker_list").value;
                                core3d(false,nus);
                            },
                        });
                        $g("marker_list").innerHTML = "<option selected>Loading...</option>";
                        datafx = await getSub("/action?room=" + $g("room_choose").value + "&action=getMark",true);
                        if(datafx["output"]==true){
                            data = datafx["message"];
                            rname = datafx["name"];
                            $g("marker_list").innerHTML = ``;
                            if (data.length > 0){
                                for (let index = 0; index < data.length; index++) {
                                    if (index == data.length - 1){
                                        if (rname[index]){
                                            // thisname = `${rname[index]}(${data[index]} - 23:59)`;
                                            thisname = `${rname[index]}`;
                                        }else{
                                            thisname = `${data[index]} - 23:59`;
                                        };
                                        $g("marker_list").innerHTML += `<option value="${data[index]}">${thisname}</option>`;

                                    }else{
                                        if (rname[index]){
                                            thisname = `${rname[index]}`;
                                        }else{
                                            thisname = `${data[index]} - ${data[index+1]}`;
                                        };
                                        $g("marker_list").innerHTML += `<option value="${data[index]}">${thisname}</option>`;
                                    }
                                    
                                }
                            }else{
                                $g("marker_list").innerHTML = `<option>{{language["bpl.nomarker"]}}</option>`;

                            }
                        }
                    };
                    return;
                };
                if (window.currentAdd){
                    addAnalystic([],true);
                    addPred([],true);
                };
                $(function() {
                    var config = $( '#studentmain' )[ 0 ].config,
                    resort = true, // or [ [0,0], [1,0] ] etc
                    callback = function( table ) {
                    };
                    $.tablesorter.updateAll( config, resort, callback );
                });
            };

    core3d(true);
    $g("show_id").onclick =function(){
        if (window.currentAdd){
            $(".eyeline").remove();
            window.currentAdd = false;
        }else{
            addAnalystic([],true);
            addPred([],true);

            window.currentAdd = true;
        };
    };
    $g("time_choose").onchange = core3d;
    $g("room_choose").onchange = core3d;

    // 
    var timeDisplay = document.getElementById("currtime");
    function refreshTime() {
        var dateString = new Date();
        stree = strfm("{0}:{1}",[
            dateString.getHours(),
            dateString.getMinutes()
        ]);
        timeDisplay.innerHTML = stree;
    };
    refreshTime();
    setInterval(refreshTime, 4000);
    setInterval(e, 1000);
    e();$g("show_id").click()

</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="/static/js/uloaderjs.js"></script>
</html>