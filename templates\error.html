<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error</title>
</head>
<script>
    document.cookie = '_ie=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    document.cookie = 'rcaching=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
</script>
<link href='https://fonts.googleapis.com/css?family=Lato:300,400,700' rel='stylesheet' type='text/css'>
<style>
    @import url("https://fonts.googleapis.com/css?family=Lato:400,700");
    html {
  height: 100%;
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
  overflow: hidden;
}

#stars {
  width: 1px;
  height: 1px;
  background: transparent;
  box-shadow: 257px 1425px #FFF , 1085px 1442px #FFF , 575px 1330px #FFF , 1468px 619px #FFF , 1256px 775px #FFF , 1591px 562px #FFF , 450px 170px #FFF , 1177px 759px #FFF , 198px 203px #FFF , 1447px 1308px #FFF , 339px 1068px #FFF , 1849px 523px #FFF , 1357px 1846px #FFF , 1185px 358px #FFF , 674px 571px #FFF , 511px 231px #FFF , 1219px 1716px #FFF , 762px 1802px #FFF , 1544px 268px #FFF , 637px 1572px #FFF , 1462px 1912px #FFF , 1634px 101px #FFF , 1236px 659px #FFF , 38px 1390px #FFF , 1010px 1273px #FFF , 1029px 847px #FFF , 821px 1881px #FFF , 1972px 602px #FFF , 64px 1032px #FFF , 293px 863px #FFF , 1799px 15px #FFF , 177px 743px #FFF , 1168px 626px #FFF , 773px 522px #FFF , 1845px 960px #FFF , 260px 1298px #FFF , 1145px 939px #FFF , 1035px 1757px #FFF , 904px 912px #FFF , 69px 170px #FFF , 1424px 1726px #FFF , 993px 1725px #FFF , 1402px 1967px #FFF , 313px 1027px #FFF , 670px 377px #FFF , 1583px 1129px #FFF , 836px 585px #FFF , 875px 1896px #FFF , 1327px 1188px #FFF , 665px 909px #FFF , 1932px 263px #FFF , 189px 447px #FFF , 647px 404px #FFF , 1560px 1667px #FFF , 321px 1021px #FFF , 880px 105px #FFF , 1817px 1794px #FFF , 1108px 1079px #FFF , 340px 1088px #FFF , 251px 892px #FFF , 975px 1307px #FFF , 815px 530px #FFF , 1854px 526px #FFF , 198px 1244px #FFF , 776px 626px #FFF , 384px 1583px #FFF , 966px 1107px #FFF , 1155px 302px #FFF , 30px 486px #FFF , 338px 499px #FFF , 138px 347px #FFF , 1318px 1169px #FFF , 112px 1127px #FFF , 1909px 871px #FFF , 733px 84px #FFF , 309px 1856px #FFF , 1116px 326px #FFF , 1296px 501px #FFF , 1980px 513px #FFF , 1445px 1891px #FFF , 476px 832px #FFF , 1128px 1702px #FFF , 1021px 1778px #FFF , 788px 1349px #FFF , 314px 1816px #FFF , 1043px 618px #FFF , 1510px 1336px #FFF , 374px 1445px #FFF , 561px 423px #FFF , 769px 67px #FFF , 592px 559px #FFF , 702px 1393px #FFF , 1270px 1102px #FFF , 654px 1931px #FFF , 558px 1908px #FFF , 1155px 654px #FFF , 594px 1003px #FFF , 749px 590px #FFF , 1499px 882px #FFF , 983px 382px #FFF , 1319px 866px #FFF , 824px 1371px #FFF , 239px 627px #FFF , 1067px 1224px #FFF , 1412px 813px #FFF , 999px 989px #FFF , 679px 901px #FFF , 1847px 163px #FFF , 1245px 1685px #FFF , 7px 1036px #FFF , 1080px 861px #FFF , 1170px 1166px #FFF , 71px 1090px #FFF , 97px 1895px #FFF , 63px 1268px #FFF , 734px 1627px #FFF , 549px 1606px #FFF , 3px 760px #FFF , 1016px 420px #FFF , 1773px 1614px #FFF , 1223px 508px #FFF , 1553px 775px #FFF , 589px 54px #FFF , 1488px 1758px #FFF , 366px 150px #FFF , 854px 1631px #FFF , 1128px 915px #FFF , 1050px 1322px #FFF , 1744px 1320px #FFF , 1260px 1709px #FFF , 1126px 1989px #FFF , 474px 1085px #FFF , 16px 54px #FFF , 511px 1736px #FFF , 657px 730px #FFF , 337px 187px #FFF , 1831px 146px #FFF , 1375px 1424px #FFF , 316px 1178px #FFF , 971px 414px #FFF , 636px 901px #FFF , 1147px 890px #FFF , 1757px 457px #FFF , 469px 1438px #FFF , 1502px 1460px #FFF , 966px 494px #FFF , 752px 666px #FFF , 1929px 1664px #FFF , 337px 868px #FFF , 1993px 287px #FFF , 1895px 611px #FFF , 986px 60px #FFF , 1988px 333px #FFF , 1749px 1006px #FFF , 1281px 1560px #FFF , 1984px 763px #FFF , 2000px 1165px #FFF , 1192px 676px #FFF , 1166px 1208px #FFF , 576px 443px #FFF , 998px 49px #FFF , 41px 1617px #FFF , 1914px 1237px #FFF , 490px 1281px #FFF , 1418px 207px #FFF , 857px 35px #FFF , 1504px 966px #FFF , 513px 871px #FFF , 697px 1654px #FFF , 1664px 133px #FFF , 1981px 556px #FFF , 377px 1156px #FFF , 1754px 218px #FFF , 788px 353px #FFF , 292px 741px #FFF , 747px 799px #FFF , 597px 757px #FFF , 509px 1791px #FFF , 37px 1193px #FFF , 577px 641px #FFF , 1572px 1344px #FFF , 1472px 816px #FFF , 1438px 1008px #FFF , 984px 122px #FFF , 195px 1291px #FFF , 1996px 949px #FFF , 259px 1201px #FFF , 1140px 624px #FFF , 1867px 770px #FFF , 1205px 220px #FFF , 1554px 1965px #FFF , 751px 1824px #FFF , 194px 377px #FFF , 1935px 873px #FFF , 959px 878px #FFF , 811px 627px #FFF , 31px 1388px #FFF , 468px 1227px #FFF , 535px 1293px #FFF , 1997px 1563px #FFF , 1054px 1133px #FFF , 533px 1144px #FFF , 952px 992px #FFF , 1173px 470px #FFF , 1046px 278px #FFF , 923px 1862px #FFF , 1571px 315px #FFF , 979px 1864px #FFF , 1003px 189px #FFF , 1921px 479px #FFF , 1491px 252px #FFF , 351px 1398px #FFF , 1358px 1849px #FFF , 452px 1002px #FFF , 1438px 1739px #FFF , 1085px 1808px #FFF , 608px 278px #FFF , 512px 1413px #FFF , 423px 184px #FFF , 512px 1394px #FFF , 1339px 1699px #FFF , 2px 1755px #FFF , 645px 1689px #FFF , 1114px 919px #FFF , 273px 1297px #FFF , 1198px 599px #FFF , 797px 1367px #FFF , 1008px 1615px #FFF , 1633px 1801px #FFF , 1683px 960px #FFF , 10px 731px #FFF , 199px 316px #FFF , 1376px 1576px #FFF , 424px 348px #FFF , 1181px 1771px #FFF , 1099px 842px #FFF , 425px 974px #FFF , 1266px 186px #FFF , 1666px 277px #FFF , 1474px 948px #FFF , 656px 1971px #FFF , 170px 1267px #FFF , 428px 346px #FFF , 1261px 111px #FFF , 1605px 434px #FFF , 1741px 1508px #FFF , 73px 1016px #FFF , 1335px 594px #FFF , 323px 1691px #FFF , 215px 95px #FFF , 1691px 1617px #FFF , 1627px 1009px #FFF , 1488px 1506px #FFF , 730px 1639px #FFF , 1461px 423px #FFF , 1345px 1037px #FFF , 735px 1553px #FFF , 135px 532px #FFF , 523px 1894px #FFF , 1403px 1922px #FFF , 1547px 163px #FFF , 1050px 262px #FFF , 404px 727px #FFF , 127px 696px #FFF , 621px 1720px #FFF , 656px 1105px #FFF , 108px 1753px #FFF , 1224px 452px #FFF , 748px 450px #FFF , 6px 1546px #FFF , 1191px 506px #FFF , 1605px 45px #FFF , 965px 489px #FFF , 1139px 1893px #FFF , 1706px 449px #FFF , 114px 1400px #FFF , 1646px 434px #FFF , 1674px 1570px #FFF , 655px 1397px #FFF , 60px 97px #FFF , 1597px 922px #FFF , 831px 215px #FFF , 1479px 678px #FFF , 1189px 193px #FFF , 895px 812px #FFF , 727px 867px #FFF , 293px 1700px #FFF , 1968px 1992px #FFF , 632px 642px #FFF , 956px 715px #FFF , 1199px 208px #FFF , 355px 1812px #FFF , 745px 1135px #FFF , 165px 1452px #FFF , 718px 1893px #FFF , 621px 1593px #FFF , 1039px 1357px #FFF , 1587px 378px #FFF , 1852px 758px #FFF , 1598px 1131px #FFF , 1676px 894px #FFF , 80px 1136px #FFF , 818px 1597px #FFF , 1945px 849px #FFF , 1657px 1002px #FFF , 1087px 1586px #FFF , 160px 1017px #FFF , 898px 1743px #FFF , 450px 1088px #FFF , 1556px 279px #FFF , 883px 627px #FFF , 872px 155px #FFF , 261px 1331px #FFF , 308px 1945px #FFF , 1241px 508px #FFF , 460px 195px #FFF , 1461px 252px #FFF , 35px 609px #FFF , 1270px 1362px #FFF , 1943px 466px #FFF , 1750px 1517px #FFF , 701px 1161px #FFF , 1380px 138px #FFF , 1567px 59px #FFF , 1997px 1226px #FFF , 1176px 572px #FFF , 785px 1383px #FFF , 67px 1810px #FFF , 1532px 1941px #FFF , 487px 519px #FFF , 1032px 270px #FFF , 277px 1038px #FFF , 102px 840px #FFF , 206px 1347px #FFF , 57px 130px #FFF , 504px 356px #FFF , 1597px 1697px #FFF , 1883px 1453px #FFF , 683px 1254px #FFF , 1043px 1630px #FFF , 1662px 1938px #FFF , 889px 1896px #FFF , 1861px 700px #FFF , 851px 805px #FFF , 1608px 547px #FFF , 155px 565px #FFF , 72px 1277px #FFF , 1621px 1592px #FFF , 1930px 1886px #FFF , 1340px 1773px #FFF , 1790px 1737px #FFF , 839px 588px #FFF , 1320px 1249px #FFF , 1588px 1834px #FFF , 943px 1929px #FFF , 1006px 468px #FFF , 23px 592px #FFF , 259px 1633px #FFF , 1258px 796px #FFF , 1843px 1737px #FFF , 1982px 1177px #FFF , 1441px 745px #FFF , 60px 1281px #FFF , 68px 1416px #FFF , 1127px 1399px #FFF , 1680px 57px #FFF , 1577px 171px #FFF , 1230px 698px #FFF , 1306px 1180px #FFF , 1800px 659px #FFF , 946px 840px #FFF , 733px 1981px #FFF , 1515px 1635px #FFF , 895px 199px #FFF , 1337px 334px #FFF , 1263px 1935px #FFF , 1525px 445px #FFF , 175px 981px #FFF , 385px 1873px #FFF , 1112px 977px #FFF , 1945px 1845px #FFF , 420px 1868px #FFF , 1350px 1122px #FFF , 1252px 1254px #FFF , 852px 209px #FFF , 535px 1228px #FFF , 1016px 998px #FFF , 438px 1550px #FFF , 888px 1031px #FFF , 215px 1937px #FFF , 1656px 691px #FFF , 1301px 1171px #FFF , 1170px 999px #FFF , 1122px 916px #FFF , 1px 197px #FFF , 1582px 450px #FFF , 22px 376px #FFF , 1622px 1146px #FFF , 253px 1933px #FFF , 397px 1570px #FFF , 1473px 1984px #FFF , 1861px 771px #FFF , 1239px 1282px #FFF , 343px 1129px #FFF , 1776px 401px #FFF , 570px 101px #FFF , 1885px 361px #FFF , 259px 1105px #FFF , 1986px 1841px #FFF , 987px 1496px #FFF , 1230px 284px #FFF , 982px 528px #FFF , 1738px 925px #FFF , 614px 1637px #FFF , 1621px 681px #FFF , 223px 429px #FFF , 1482px 1542px #FFF , 1356px 1868px #FFF , 1925px 1993px #FFF , 1911px 1695px #FFF , 40px 1072px #FFF , 1980px 1663px #FFF , 1567px 979px #FFF , 334px 1914px #FFF , 1129px 1444px #FFF , 1239px 71px #FFF , 137px 665px #FFF , 1438px 384px #FFF , 51px 881px #FFF , 562px 1766px #FFF , 1687px 691px #FFF , 838px 495px #FFF , 1958px 1428px #FFF , 1781px 1828px #FFF , 300px 1363px #FFF , 1330px 1785px #FFF , 311px 1251px #FFF , 1967px 218px #FFF , 312px 1295px #FFF , 570px 4px #FFF , 1454px 175px #FFF , 1261px 11px #FFF , 22px 899px #FFF , 405px 1127px #FFF , 1413px 821px #FFF , 1653px 1408px #FFF , 1707px 1277px #FFF , 1301px 1113px #FFF , 1078px 742px #FFF , 1749px 1784px #FFF , 374px 1808px #FFF , 159px 1582px #FFF , 1022px 558px #FFF , 1036px 1910px #FFF , 1756px 235px #FFF , 83px 957px #FFF , 1724px 1766px #FFF , 324px 792px #FFF , 189px 1112px #FFF , 1545px 1871px #FFF , 509px 1722px #FFF , 21px 899px #FFF , 1935px 741px #FFF , 1710px 864px #FFF , 1995px 1576px #FFF , 631px 246px #FFF , 354px 1596px #FFF , 1601px 1606px #FFF , 1091px 441px #FFF , 1570px 1693px #FFF , 1183px 913px #FFF , 457px 802px #FFF , 185px 1745px #FFF , 931px 1030px #FFF , 1886px 1280px #FFF , 1629px 352px #FFF , 635px 220px #FFF , 1864px 935px #FFF , 823px 683px #FFF , 1179px 433px #FFF , 1282px 92px #FFF , 1474px 845px #FFF , 623px 807px #FFF , 266px 1594px #FFF , 1124px 1142px #FFF , 1156px 862px #FFF , 1512px 424px #FFF , 1311px 349px #FFF , 712px 551px #FFF , 1024px 1735px #FFF , 472px 1923px #FFF , 25px 81px #FFF , 1559px 176px #FFF , 488px 1156px #FFF , 1328px 1489px #FFF , 523px 238px #FFF , 1123px 1610px #FFF , 1502px 323px #FFF , 1551px 256px #FFF , 155px 1744px #FFF , 106px 1101px #FFF , 1909px 385px #FFF , 78px 1652px #FFF , 1934px 1065px #FFF , 498px 1310px #FFF , 1530px 957px #FFF , 628px 1437px #FFF , 183px 1345px #FFF , 598px 1916px #FFF , 49px 384px #FFF , 350px 1598px #FFF , 1821px 1587px #FFF , 1979px 98px #FFF , 451px 88px #FFF , 1796px 1546px #FFF , 1877px 660px #FFF , 1379px 1668px #FFF , 1790px 1088px #FFF , 1908px 1744px #FFF , 1971px 1889px #FFF , 1891px 252px #FFF , 54px 1274px #FFF , 1511px 1830px #FFF , 964px 978px #FFF , 1372px 1087px #FFF , 282px 111px #FFF , 1392px 1410px #FFF , 1486px 207px #FFF , 1345px 204px #FFF , 672px 1944px #FFF , 955px 250px #FFF , 1626px 1543px #FFF , 903px 1859px #FFF , 1116px 1704px #FFF , 540px 491px #FFF , 1875px 1175px #FFF , 1697px 1029px #FFF , 1835px 264px #FFF , 1676px 1032px #FFF , 34px 557px #FFF , 1002px 234px #FFF , 508px 847px #FFF , 1090px 1589px #FFF , 184px 1963px #FFF , 532px 107px #FFF , 1216px 24px #FFF , 1149px 113px #FFF , 1957px 1401px #FFF , 902px 495px #FFF , 1367px 692px #FFF , 1356px 545px #FFF , 423px 505px #FFF , 702px 278px #FFF , 1296px 406px #FFF , 999px 256px #FFF , 1314px 1242px #FFF , 1928px 1165px #FFF , 1425px 1312px #FFF , 682px 719px #FFF , 1558px 645px #FFF , 1074px 939px #FFF , 1577px 283px #FFF , 1263px 138px #FFF , 257px 1806px #FFF , 1186px 865px #FFF , 589px 1583px #FFF , 1070px 1571px #FFF , 1615px 1458px #FFF , 1462px 489px #FFF , 1207px 857px #FFF , 690px 944px #FFF , 1534px 1109px #FFF , 753px 1532px #FFF , 862px 153px #FFF , 1548px 212px #FFF , 725px 1736px #FFF , 943px 1272px #FFF , 1986px 1528px #FFF , 871px 1215px #FFF , 436px 1171px #FFF , 822px 310px #FFF , 349px 1040px #FFF , 810px 948px #FFF , 1363px 1829px #FFF , 1413px 628px #FFF , 1039px 1722px #FFF , 464px 315px #FFF , 233px 1409px #FFF , 1220px 563px #FFF , 1448px 1421px #FFF , 1429px 1761px #FFF , 1887px 702px #FFF , 740px 779px #FFF , 737px 1696px #FFF , 1013px 372px #FFF , 1728px 395px #FFF , 1874px 890px #FFF , 1915px 1767px #FFF , 910px 1395px #FFF , 1703px 739px #FFF , 896px 1820px #FFF , 375px 1009px #FFF , 1537px 596px #FFF , 1949px 1728px #FFF , 1973px 1215px #FFF , 22px 1519px #FFF , 1913px 1578px #FFF , 1897px 1078px #FFF , 119px 1112px #FFF , 800px 310px #FFF , 1724px 1069px #FFF , 1888px 1273px #FFF , 716px 272px #FFF , 725px 469px #FFF , 326px 759px #FFF , 625px 1730px #FFF , 1390px 469px #FFF , 1905px 1436px #FFF , 1050px 917px #FFF , 977px 758px #FFF , 1104px 896px #FFF , 1736px 207px #FFF , 756px 1881px #FFF , 970px 1613px #FFF , 210px 891px #FFF , 1226px 302px #FFF , 1051px 1569px #FFF , 1232px 502px #FFF , 379px 1141px #FFF , 308px 1999px #FFF , 333px 1789px #FFF , 1412px 1030px #FFF , 1571px 817px #FFF , 1615px 1642px #FFF , 788px 112px #FFF , 1127px 688px #FFF , 673px 212px #FFF , 920px 1971px #FFF , 238px 1292px #FFF , 1498px 704px #FFF , 420px 1114px #FFF , 1307px 62px #FFF , 406px 930px #FFF , 1082px 1818px #FFF , 536px 544px #FFF , 467px 1276px #FFF , 207px 1674px #FFF , 126px 1512px #FFF , 1117px 1202px #FFF , 594px 1686px #FFF , 979px 718px #FFF , 1373px 1113px #FFF , 941px 377px #FFF , 1668px 348px #FFF , 221px 1633px #FFF , 817px 1986px #FFF , 1779px 949px #FFF , 8px 1073px #FFF , 1551px 134px #FFF , 270px 1437px #FFF , 806px 144px #FFF , 18px 1812px #FFF , 1129px 1170px #FFF , 469px 933px #FFF , 1575px 973px #FFF , 577px 1921px #FFF , 1267px 214px #FFF , 1791px 1677px #FFF , 1303px 1706px #FFF , 299px 1004px #FFF , 345px 1799px #FFF , 712px 112px #FFF , 396px 17px #FFF , 146px 1836px #FFF , 1775px 1324px #FFF , 668px 1208px #FFF , 289px 892px #FFF , 1664px 1463px #FFF , 213px 1960px #FFF , 1594px 1080px #FFF , 1695px 686px #FFF , 1482px 1938px #FFF , 782px 924px #FFF , 1705px 1179px #FFF , 107px 1852px #FFF , 1239px 752px #FFF , 1383px 255px #FFF , 440px 1935px #FFF , 396px 1011px #FFF , 1402px 1992px #FFF , 820px 11px #FFF , 716px 1086px #FFF , 1801px 853px #FFF , 490px 902px #FFF , 912px 556px #FFF , 1438px 1609px #FFF , 627px 1661px #FFF , 1450px 1709px #FFF , 1486px 1240px #FFF , 36px 384px #FFF;
  animation: animStar 50s linear infinite;
}
#stars:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 1px;
  height: 1px;
  background: transparent;
  box-shadow: 257px 1425px #FFF , 1085px 1442px #FFF , 575px 1330px #FFF , 1468px 619px #FFF , 1256px 775px #FFF , 1591px 562px #FFF , 450px 170px #FFF , 1177px 759px #FFF , 198px 203px #FFF , 1447px 1308px #FFF , 339px 1068px #FFF , 1849px 523px #FFF , 1357px 1846px #FFF , 1185px 358px #FFF , 674px 571px #FFF , 511px 231px #FFF , 1219px 1716px #FFF , 762px 1802px #FFF , 1544px 268px #FFF , 637px 1572px #FFF , 1462px 1912px #FFF , 1634px 101px #FFF , 1236px 659px #FFF , 38px 1390px #FFF , 1010px 1273px #FFF , 1029px 847px #FFF , 821px 1881px #FFF , 1972px 602px #FFF , 64px 1032px #FFF , 293px 863px #FFF , 1799px 15px #FFF , 177px 743px #FFF , 1168px 626px #FFF , 773px 522px #FFF , 1845px 960px #FFF , 260px 1298px #FFF , 1145px 939px #FFF , 1035px 1757px #FFF , 904px 912px #FFF , 69px 170px #FFF , 1424px 1726px #FFF , 993px 1725px #FFF , 1402px 1967px #FFF , 313px 1027px #FFF , 670px 377px #FFF , 1583px 1129px #FFF , 836px 585px #FFF , 875px 1896px #FFF , 1327px 1188px #FFF , 665px 909px #FFF , 1932px 263px #FFF , 189px 447px #FFF , 647px 404px #FFF , 1560px 1667px #FFF , 321px 1021px #FFF , 880px 105px #FFF , 1817px 1794px #FFF , 1108px 1079px #FFF , 340px 1088px #FFF , 251px 892px #FFF , 975px 1307px #FFF , 815px 530px #FFF , 1854px 526px #FFF , 198px 1244px #FFF , 776px 626px #FFF , 384px 1583px #FFF , 966px 1107px #FFF , 1155px 302px #FFF , 30px 486px #FFF , 338px 499px #FFF , 138px 347px #FFF , 1318px 1169px #FFF , 112px 1127px #FFF , 1909px 871px #FFF , 733px 84px #FFF , 309px 1856px #FFF , 1116px 326px #FFF , 1296px 501px #FFF , 1980px 513px #FFF , 1445px 1891px #FFF , 476px 832px #FFF , 1128px 1702px #FFF , 1021px 1778px #FFF , 788px 1349px #FFF , 314px 1816px #FFF , 1043px 618px #FFF , 1510px 1336px #FFF , 374px 1445px #FFF , 561px 423px #FFF , 769px 67px #FFF , 592px 559px #FFF , 702px 1393px #FFF , 1270px 1102px #FFF , 654px 1931px #FFF , 558px 1908px #FFF , 1155px 654px #FFF , 594px 1003px #FFF , 749px 590px #FFF , 1499px 882px #FFF , 983px 382px #FFF , 1319px 866px #FFF , 824px 1371px #FFF , 239px 627px #FFF , 1067px 1224px #FFF , 1412px 813px #FFF , 999px 989px #FFF , 679px 901px #FFF , 1847px 163px #FFF , 1245px 1685px #FFF , 7px 1036px #FFF , 1080px 861px #FFF , 1170px 1166px #FFF , 71px 1090px #FFF , 97px 1895px #FFF , 63px 1268px #FFF , 734px 1627px #FFF , 549px 1606px #FFF , 3px 760px #FFF , 1016px 420px #FFF , 1773px 1614px #FFF , 1223px 508px #FFF , 1553px 775px #FFF , 589px 54px #FFF , 1488px 1758px #FFF , 366px 150px #FFF , 854px 1631px #FFF , 1128px 915px #FFF , 1050px 1322px #FFF , 1744px 1320px #FFF , 1260px 1709px #FFF , 1126px 1989px #FFF , 474px 1085px #FFF , 16px 54px #FFF , 511px 1736px #FFF , 657px 730px #FFF , 337px 187px #FFF , 1831px 146px #FFF , 1375px 1424px #FFF , 316px 1178px #FFF , 971px 414px #FFF , 636px 901px #FFF , 1147px 890px #FFF , 1757px 457px #FFF , 469px 1438px #FFF , 1502px 1460px #FFF , 966px 494px #FFF , 752px 666px #FFF , 1929px 1664px #FFF , 337px 868px #FFF , 1993px 287px #FFF , 1895px 611px #FFF , 986px 60px #FFF , 1988px 333px #FFF , 1749px 1006px #FFF , 1281px 1560px #FFF , 1984px 763px #FFF , 2000px 1165px #FFF , 1192px 676px #FFF , 1166px 1208px #FFF , 576px 443px #FFF , 998px 49px #FFF , 41px 1617px #FFF , 1914px 1237px #FFF , 490px 1281px #FFF , 1418px 207px #FFF , 857px 35px #FFF , 1504px 966px #FFF , 513px 871px #FFF , 697px 1654px #FFF , 1664px 133px #FFF , 1981px 556px #FFF , 377px 1156px #FFF , 1754px 218px #FFF , 788px 353px #FFF , 292px 741px #FFF , 747px 799px #FFF , 597px 757px #FFF , 509px 1791px #FFF , 37px 1193px #FFF , 577px 641px #FFF , 1572px 1344px #FFF , 1472px 816px #FFF , 1438px 1008px #FFF , 984px 122px #FFF , 195px 1291px #FFF , 1996px 949px #FFF , 259px 1201px #FFF , 1140px 624px #FFF , 1867px 770px #FFF , 1205px 220px #FFF , 1554px 1965px #FFF , 751px 1824px #FFF , 194px 377px #FFF , 1935px 873px #FFF , 959px 878px #FFF , 811px 627px #FFF , 31px 1388px #FFF , 468px 1227px #FFF , 535px 1293px #FFF , 1997px 1563px #FFF , 1054px 1133px #FFF , 533px 1144px #FFF , 952px 992px #FFF , 1173px 470px #FFF , 1046px 278px #FFF , 923px 1862px #FFF , 1571px 315px #FFF , 979px 1864px #FFF , 1003px 189px #FFF , 1921px 479px #FFF , 1491px 252px #FFF , 351px 1398px #FFF , 1358px 1849px #FFF , 452px 1002px #FFF , 1438px 1739px #FFF , 1085px 1808px #FFF , 608px 278px #FFF , 512px 1413px #FFF , 423px 184px #FFF , 512px 1394px #FFF , 1339px 1699px #FFF , 2px 1755px #FFF , 645px 1689px #FFF , 1114px 919px #FFF , 273px 1297px #FFF , 1198px 599px #FFF , 797px 1367px #FFF , 1008px 1615px #FFF , 1633px 1801px #FFF , 1683px 960px #FFF , 10px 731px #FFF , 199px 316px #FFF , 1376px 1576px #FFF , 424px 348px #FFF , 1181px 1771px #FFF , 1099px 842px #FFF , 425px 974px #FFF , 1266px 186px #FFF , 1666px 277px #FFF , 1474px 948px #FFF , 656px 1971px #FFF , 170px 1267px #FFF , 428px 346px #FFF , 1261px 111px #FFF , 1605px 434px #FFF , 1741px 1508px #FFF , 73px 1016px #FFF , 1335px 594px #FFF , 323px 1691px #FFF , 215px 95px #FFF , 1691px 1617px #FFF , 1627px 1009px #FFF , 1488px 1506px #FFF , 730px 1639px #FFF , 1461px 423px #FFF , 1345px 1037px #FFF , 735px 1553px #FFF , 135px 532px #FFF , 523px 1894px #FFF , 1403px 1922px #FFF , 1547px 163px #FFF , 1050px 262px #FFF , 404px 727px #FFF , 127px 696px #FFF , 621px 1720px #FFF , 656px 1105px #FFF , 108px 1753px #FFF , 1224px 452px #FFF , 748px 450px #FFF , 6px 1546px #FFF , 1191px 506px #FFF , 1605px 45px #FFF , 965px 489px #FFF , 1139px 1893px #FFF , 1706px 449px #FFF , 114px 1400px #FFF , 1646px 434px #FFF , 1674px 1570px #FFF , 655px 1397px #FFF , 60px 97px #FFF , 1597px 922px #FFF , 831px 215px #FFF , 1479px 678px #FFF , 1189px 193px #FFF , 895px 812px #FFF , 727px 867px #FFF , 293px 1700px #FFF , 1968px 1992px #FFF , 632px 642px #FFF , 956px 715px #FFF , 1199px 208px #FFF , 355px 1812px #FFF , 745px 1135px #FFF , 165px 1452px #FFF , 718px 1893px #FFF , 621px 1593px #FFF , 1039px 1357px #FFF , 1587px 378px #FFF , 1852px 758px #FFF , 1598px 1131px #FFF , 1676px 894px #FFF , 80px 1136px #FFF , 818px 1597px #FFF , 1945px 849px #FFF , 1657px 1002px #FFF , 1087px 1586px #FFF , 160px 1017px #FFF , 898px 1743px #FFF , 450px 1088px #FFF , 1556px 279px #FFF , 883px 627px #FFF , 872px 155px #FFF , 261px 1331px #FFF , 308px 1945px #FFF , 1241px 508px #FFF , 460px 195px #FFF , 1461px 252px #FFF , 35px 609px #FFF , 1270px 1362px #FFF , 1943px 466px #FFF , 1750px 1517px #FFF , 701px 1161px #FFF , 1380px 138px #FFF , 1567px 59px #FFF , 1997px 1226px #FFF , 1176px 572px #FFF , 785px 1383px #FFF , 67px 1810px #FFF , 1532px 1941px #FFF , 487px 519px #FFF , 1032px 270px #FFF , 277px 1038px #FFF , 102px 840px #FFF , 206px 1347px #FFF , 57px 130px #FFF , 504px 356px #FFF , 1597px 1697px #FFF , 1883px 1453px #FFF , 683px 1254px #FFF , 1043px 1630px #FFF , 1662px 1938px #FFF , 889px 1896px #FFF , 1861px 700px #FFF , 851px 805px #FFF , 1608px 547px #FFF , 155px 565px #FFF , 72px 1277px #FFF , 1621px 1592px #FFF , 1930px 1886px #FFF , 1340px 1773px #FFF , 1790px 1737px #FFF , 839px 588px #FFF , 1320px 1249px #FFF , 1588px 1834px #FFF , 943px 1929px #FFF , 1006px 468px #FFF , 23px 592px #FFF , 259px 1633px #FFF , 1258px 796px #FFF , 1843px 1737px #FFF , 1982px 1177px #FFF , 1441px 745px #FFF , 60px 1281px #FFF , 68px 1416px #FFF , 1127px 1399px #FFF , 1680px 57px #FFF , 1577px 171px #FFF , 1230px 698px #FFF , 1306px 1180px #FFF , 1800px 659px #FFF , 946px 840px #FFF , 733px 1981px #FFF , 1515px 1635px #FFF , 895px 199px #FFF , 1337px 334px #FFF , 1263px 1935px #FFF , 1525px 445px #FFF , 175px 981px #FFF , 385px 1873px #FFF , 1112px 977px #FFF , 1945px 1845px #FFF , 420px 1868px #FFF , 1350px 1122px #FFF , 1252px 1254px #FFF , 852px 209px #FFF , 535px 1228px #FFF , 1016px 998px #FFF , 438px 1550px #FFF , 888px 1031px #FFF , 215px 1937px #FFF , 1656px 691px #FFF , 1301px 1171px #FFF , 1170px 999px #FFF , 1122px 916px #FFF , 1px 197px #FFF , 1582px 450px #FFF , 22px 376px #FFF , 1622px 1146px #FFF , 253px 1933px #FFF , 397px 1570px #FFF , 1473px 1984px #FFF , 1861px 771px #FFF , 1239px 1282px #FFF , 343px 1129px #FFF , 1776px 401px #FFF , 570px 101px #FFF , 1885px 361px #FFF , 259px 1105px #FFF , 1986px 1841px #FFF , 987px 1496px #FFF , 1230px 284px #FFF , 982px 528px #FFF , 1738px 925px #FFF , 614px 1637px #FFF , 1621px 681px #FFF , 223px 429px #FFF , 1482px 1542px #FFF , 1356px 1868px #FFF , 1925px 1993px #FFF , 1911px 1695px #FFF , 40px 1072px #FFF , 1980px 1663px #FFF , 1567px 979px #FFF , 334px 1914px #FFF , 1129px 1444px #FFF , 1239px 71px #FFF , 137px 665px #FFF , 1438px 384px #FFF , 51px 881px #FFF , 562px 1766px #FFF , 1687px 691px #FFF , 838px 495px #FFF , 1958px 1428px #FFF , 1781px 1828px #FFF , 300px 1363px #FFF , 1330px 1785px #FFF , 311px 1251px #FFF , 1967px 218px #FFF , 312px 1295px #FFF , 570px 4px #FFF , 1454px 175px #FFF , 1261px 11px #FFF , 22px 899px #FFF , 405px 1127px #FFF , 1413px 821px #FFF , 1653px 1408px #FFF , 1707px 1277px #FFF , 1301px 1113px #FFF , 1078px 742px #FFF , 1749px 1784px #FFF , 374px 1808px #FFF , 159px 1582px #FFF , 1022px 558px #FFF , 1036px 1910px #FFF , 1756px 235px #FFF , 83px 957px #FFF , 1724px 1766px #FFF , 324px 792px #FFF , 189px 1112px #FFF , 1545px 1871px #FFF , 509px 1722px #FFF , 21px 899px #FFF , 1935px 741px #FFF , 1710px 864px #FFF , 1995px 1576px #FFF , 631px 246px #FFF , 354px 1596px #FFF , 1601px 1606px #FFF , 1091px 441px #FFF , 1570px 1693px #FFF , 1183px 913px #FFF , 457px 802px #FFF , 185px 1745px #FFF , 931px 1030px #FFF , 1886px 1280px #FFF , 1629px 352px #FFF , 635px 220px #FFF , 1864px 935px #FFF , 823px 683px #FFF , 1179px 433px #FFF , 1282px 92px #FFF , 1474px 845px #FFF , 623px 807px #FFF , 266px 1594px #FFF , 1124px 1142px #FFF , 1156px 862px #FFF , 1512px 424px #FFF , 1311px 349px #FFF , 712px 551px #FFF , 1024px 1735px #FFF , 472px 1923px #FFF , 25px 81px #FFF , 1559px 176px #FFF , 488px 1156px #FFF , 1328px 1489px #FFF , 523px 238px #FFF , 1123px 1610px #FFF , 1502px 323px #FFF , 1551px 256px #FFF , 155px 1744px #FFF , 106px 1101px #FFF , 1909px 385px #FFF , 78px 1652px #FFF , 1934px 1065px #FFF , 498px 1310px #FFF , 1530px 957px #FFF , 628px 1437px #FFF , 183px 1345px #FFF , 598px 1916px #FFF , 49px 384px #FFF , 350px 1598px #FFF , 1821px 1587px #FFF , 1979px 98px #FFF , 451px 88px #FFF , 1796px 1546px #FFF , 1877px 660px #FFF , 1379px 1668px #FFF , 1790px 1088px #FFF , 1908px 1744px #FFF , 1971px 1889px #FFF , 1891px 252px #FFF , 54px 1274px #FFF , 1511px 1830px #FFF , 964px 978px #FFF , 1372px 1087px #FFF , 282px 111px #FFF , 1392px 1410px #FFF , 1486px 207px #FFF , 1345px 204px #FFF , 672px 1944px #FFF , 955px 250px #FFF , 1626px 1543px #FFF , 903px 1859px #FFF , 1116px 1704px #FFF , 540px 491px #FFF , 1875px 1175px #FFF , 1697px 1029px #FFF , 1835px 264px #FFF , 1676px 1032px #FFF , 34px 557px #FFF , 1002px 234px #FFF , 508px 847px #FFF , 1090px 1589px #FFF , 184px 1963px #FFF , 532px 107px #FFF , 1216px 24px #FFF , 1149px 113px #FFF , 1957px 1401px #FFF , 902px 495px #FFF , 1367px 692px #FFF , 1356px 545px #FFF , 423px 505px #FFF , 702px 278px #FFF , 1296px 406px #FFF , 999px 256px #FFF , 1314px 1242px #FFF , 1928px 1165px #FFF , 1425px 1312px #FFF , 682px 719px #FFF , 1558px 645px #FFF , 1074px 939px #FFF , 1577px 283px #FFF , 1263px 138px #FFF , 257px 1806px #FFF , 1186px 865px #FFF , 589px 1583px #FFF , 1070px 1571px #FFF , 1615px 1458px #FFF , 1462px 489px #FFF , 1207px 857px #FFF , 690px 944px #FFF , 1534px 1109px #FFF , 753px 1532px #FFF , 862px 153px #FFF , 1548px 212px #FFF , 725px 1736px #FFF , 943px 1272px #FFF , 1986px 1528px #FFF , 871px 1215px #FFF , 436px 1171px #FFF , 822px 310px #FFF , 349px 1040px #FFF , 810px 948px #FFF , 1363px 1829px #FFF , 1413px 628px #FFF , 1039px 1722px #FFF , 464px 315px #FFF , 233px 1409px #FFF , 1220px 563px #FFF , 1448px 1421px #FFF , 1429px 1761px #FFF , 1887px 702px #FFF , 740px 779px #FFF , 737px 1696px #FFF , 1013px 372px #FFF , 1728px 395px #FFF , 1874px 890px #FFF , 1915px 1767px #FFF , 910px 1395px #FFF , 1703px 739px #FFF , 896px 1820px #FFF , 375px 1009px #FFF , 1537px 596px #FFF , 1949px 1728px #FFF , 1973px 1215px #FFF , 22px 1519px #FFF , 1913px 1578px #FFF , 1897px 1078px #FFF , 119px 1112px #FFF , 800px 310px #FFF , 1724px 1069px #FFF , 1888px 1273px #FFF , 716px 272px #FFF , 725px 469px #FFF , 326px 759px #FFF , 625px 1730px #FFF , 1390px 469px #FFF , 1905px 1436px #FFF , 1050px 917px #FFF , 977px 758px #FFF , 1104px 896px #FFF , 1736px 207px #FFF , 756px 1881px #FFF , 970px 1613px #FFF , 210px 891px #FFF , 1226px 302px #FFF , 1051px 1569px #FFF , 1232px 502px #FFF , 379px 1141px #FFF , 308px 1999px #FFF , 333px 1789px #FFF , 1412px 1030px #FFF , 1571px 817px #FFF , 1615px 1642px #FFF , 788px 112px #FFF , 1127px 688px #FFF , 673px 212px #FFF , 920px 1971px #FFF , 238px 1292px #FFF , 1498px 704px #FFF , 420px 1114px #FFF , 1307px 62px #FFF , 406px 930px #FFF , 1082px 1818px #FFF , 536px 544px #FFF , 467px 1276px #FFF , 207px 1674px #FFF , 126px 1512px #FFF , 1117px 1202px #FFF , 594px 1686px #FFF , 979px 718px #FFF , 1373px 1113px #FFF , 941px 377px #FFF , 1668px 348px #FFF , 221px 1633px #FFF , 817px 1986px #FFF , 1779px 949px #FFF , 8px 1073px #FFF , 1551px 134px #FFF , 270px 1437px #FFF , 806px 144px #FFF , 18px 1812px #FFF , 1129px 1170px #FFF , 469px 933px #FFF , 1575px 973px #FFF , 577px 1921px #FFF , 1267px 214px #FFF , 1791px 1677px #FFF , 1303px 1706px #FFF , 299px 1004px #FFF , 345px 1799px #FFF , 712px 112px #FFF , 396px 17px #FFF , 146px 1836px #FFF , 1775px 1324px #FFF , 668px 1208px #FFF , 289px 892px #FFF , 1664px 1463px #FFF , 213px 1960px #FFF , 1594px 1080px #FFF , 1695px 686px #FFF , 1482px 1938px #FFF , 782px 924px #FFF , 1705px 1179px #FFF , 107px 1852px #FFF , 1239px 752px #FFF , 1383px 255px #FFF , 440px 1935px #FFF , 396px 1011px #FFF , 1402px 1992px #FFF , 820px 11px #FFF , 716px 1086px #FFF , 1801px 853px #FFF , 490px 902px #FFF , 912px 556px #FFF , 1438px 1609px #FFF , 627px 1661px #FFF , 1450px 1709px #FFF , 1486px 1240px #FFF , 36px 384px #FFF;
}

#stars2 {
  width: 2px;
  height: 2px;
  background: transparent;
  box-shadow: 256px 1196px #FFF , 168px 693px #FFF , 373px 586px #FFF , 1106px 1094px #FFF , 583px 142px #FFF , 664px 1560px #FFF , 193px 1434px #FFF , 51px 539px #FFF , 1960px 1765px #FFF , 1234px 1105px #FFF , 963px 720px #FFF , 1131px 1085px #FFF , 984px 97px #FFF , 938px 1359px #FFF , 1877px 1459px #FFF , 1838px 1744px #FFF , 767px 1808px #FFF , 1677px 1913px #FFF , 1362px 1334px #FFF , 187px 1035px #FFF , 1691px 1424px #FFF , 1370px 96px #FFF , 105px 1588px #FFF , 81px 299px #FFF , 1263px 1750px #FFF , 1496px 962px #FFF , 564px 1660px #FFF , 845px 1714px #FFF , 729px 1029px #FFF , 312px 803px #FFF , 157px 1058px #FFF , 955px 737px #FFF , 100px 1519px #FFF , 1896px 1378px #FFF , 905px 1839px #FFF , 1786px 845px #FFF , 165px 1903px #FFF , 1602px 143px #FFF , 978px 324px #FFF , 597px 219px #FFF , 1955px 1104px #FFF , 1854px 370px #FFF , 1971px 166px #FFF , 1973px 1815px #FFF , 606px 269px #FFF , 1777px 1350px #FFF , 365px 1232px #FFF , 1843px 3px #FFF , 352px 391px #FFF , 1676px 1521px #FFF , 482px 481px #FFF , 1280px 1528px #FFF , 1154px 1358px #FFF , 1076px 340px #FFF , 800px 1878px #FFF , 1011px 1523px #FFF , 1772px 1200px #FFF , 1795px 1125px #FFF , 1353px 1885px #FFF , 1187px 226px #FFF , 1253px 109px #FFF , 1296px 1858px #FFF , 1977px 1205px #FFF , 613px 467px #FFF , 1260px 406px #FFF , 570px 661px #FFF , 708px 783px #FFF , 218px 1929px #FFF , 1238px 927px #FFF , 635px 249px #FFF , 166px 997px #FFF , 1713px 1886px #FFF , 1647px 1154px #FFF , 1332px 1461px #FFF , 180px 1752px #FFF , 1954px 1373px #FFF , 1306px 1398px #FFF , 1634px 440px #FFF , 1112px 1776px #FFF , 1899px 974px #FFF , 129px 310px #FFF , 270px 698px #FFF , 1193px 1934px #FFF , 159px 1416px #FFF , 1559px 1853px #FFF , 566px 411px #FFF , 1996px 571px #FFF , 560px 542px #FFF , 1177px 1508px #FFF , 1014px 1813px #FFF , 1350px 1120px #FFF , 41px 655px #FFF , 76px 1676px #FFF , 1649px 1312px #FFF , 1844px 499px #FFF , 96px 934px #FFF , 1910px 363px #FFF , 836px 1186px #FFF , 2000px 111px #FFF , 561px 410px #FFF , 1750px 470px #FFF , 725px 801px #FFF , 775px 265px #FFF , 669px 468px #FFF , 643px 1473px #FFF , 1788px 1214px #FFF , 1987px 1832px #FFF , 925px 1839px #FFF , 620px 1324px #FFF , 1776px 257px #FFF , 1572px 302px #FFF , 508px 22px #FFF , 413px 1590px #FFF , 302px 217px #FFF , 432px 1956px #FFF , 499px 639px #FFF , 1827px 1248px #FFF , 726px 1667px #FFF , 873px 1204px #FFF , 1635px 1752px #FFF , 672px 850px #FFF , 1370px 1699px #FFF , 1313px 1458px #FFF , 1839px 1899px #FFF , 1813px 1794px #FFF , 429px 1628px #FFF , 1896px 1792px #FFF , 1952px 1988px #FFF , 685px 61px #FFF , 1456px 1311px #FFF , 47px 331px #FFF , 1225px 1121px #FFF , 1309px 1579px #FFF , 1887px 617px #FFF , 250px 875px #FFF , 330px 748px #FFF , 1902px 1729px #FFF , 616px 80px #FFF , 830px 1545px #FFF , 1804px 1979px #FFF , 1170px 243px #FFF , 1760px 1060px #FFF , 117px 1181px #FFF , 1424px 762px #FFF , 998px 197px #FFF , 1661px 1778px #FFF , 395px 1770px #FFF , 1195px 193px #FFF , 1927px 303px #FFF , 721px 1640px #FFF , 941px 207px #FFF , 1026px 1986px #FFF , 729px 1812px #FFF , 119px 836px #FFF , 739px 1179px #FFF , 1940px 847px #FFF , 1230px 1109px #FFF , 893px 116px #FFF , 934px 735px #FFF , 19px 718px #FFF , 43px 206px #FFF , 1002px 27px #FFF , 1966px 1639px #FFF , 160px 1087px #FFF , 1227px 1614px #FFF , 1705px 1037px #FFF , 422px 1893px #FFF , 1737px 679px #FFF , 1482px 74px #FFF , 151px 226px #FFF , 1280px 1901px #FFF , 1795px 703px #FFF , 1446px 373px #FFF , 1380px 29px #FFF , 1851px 1890px #FFF , 816px 1296px #FFF , 942px 1985px #FFF , 1994px 446px #FFF , 183px 1746px #FFF , 1747px 1294px #FFF , 1760px 953px #FFF , 1174px 1623px #FFF , 144px 21px #FFF , 1980px 593px #FFF , 1714px 1610px #FFF , 1284px 899px #FFF , 1285px 1418px #FFF , 96px 170px #FFF , 177px 1654px #FFF , 1969px 123px #FFF , 1168px 1351px #FFF , 996px 726px #FFF , 351px 982px #FFF , 1607px 967px #FFF , 1702px 1847px #FFF , 611px 1752px #FFF , 1327px 899px #FFF , 1101px 141px #FFF , 876px 1558px #FFF , 1408px 409px #FFF;
  animation: animStar 100s linear infinite;
}
#stars2:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 2px;
  height: 2px;
  background: transparent;
  box-shadow: 256px 1196px #FFF , 168px 693px #FFF , 373px 586px #FFF , 1106px 1094px #FFF , 583px 142px #FFF , 664px 1560px #FFF , 193px 1434px #FFF , 51px 539px #FFF , 1960px 1765px #FFF , 1234px 1105px #FFF , 963px 720px #FFF , 1131px 1085px #FFF , 984px 97px #FFF , 938px 1359px #FFF , 1877px 1459px #FFF , 1838px 1744px #FFF , 767px 1808px #FFF , 1677px 1913px #FFF , 1362px 1334px #FFF , 187px 1035px #FFF , 1691px 1424px #FFF , 1370px 96px #FFF , 105px 1588px #FFF , 81px 299px #FFF , 1263px 1750px #FFF , 1496px 962px #FFF , 564px 1660px #FFF , 845px 1714px #FFF , 729px 1029px #FFF , 312px 803px #FFF , 157px 1058px #FFF , 955px 737px #FFF , 100px 1519px #FFF , 1896px 1378px #FFF , 905px 1839px #FFF , 1786px 845px #FFF , 165px 1903px #FFF , 1602px 143px #FFF , 978px 324px #FFF , 597px 219px #FFF , 1955px 1104px #FFF , 1854px 370px #FFF , 1971px 166px #FFF , 1973px 1815px #FFF , 606px 269px #FFF , 1777px 1350px #FFF , 365px 1232px #FFF , 1843px 3px #FFF , 352px 391px #FFF , 1676px 1521px #FFF , 482px 481px #FFF , 1280px 1528px #FFF , 1154px 1358px #FFF , 1076px 340px #FFF , 800px 1878px #FFF , 1011px 1523px #FFF , 1772px 1200px #FFF , 1795px 1125px #FFF , 1353px 1885px #FFF , 1187px 226px #FFF , 1253px 109px #FFF , 1296px 1858px #FFF , 1977px 1205px #FFF , 613px 467px #FFF , 1260px 406px #FFF , 570px 661px #FFF , 708px 783px #FFF , 218px 1929px #FFF , 1238px 927px #FFF , 635px 249px #FFF , 166px 997px #FFF , 1713px 1886px #FFF , 1647px 1154px #FFF , 1332px 1461px #FFF , 180px 1752px #FFF , 1954px 1373px #FFF , 1306px 1398px #FFF , 1634px 440px #FFF , 1112px 1776px #FFF , 1899px 974px #FFF , 129px 310px #FFF , 270px 698px #FFF , 1193px 1934px #FFF , 159px 1416px #FFF , 1559px 1853px #FFF , 566px 411px #FFF , 1996px 571px #FFF , 560px 542px #FFF , 1177px 1508px #FFF , 1014px 1813px #FFF , 1350px 1120px #FFF , 41px 655px #FFF , 76px 1676px #FFF , 1649px 1312px #FFF , 1844px 499px #FFF , 96px 934px #FFF , 1910px 363px #FFF , 836px 1186px #FFF , 2000px 111px #FFF , 561px 410px #FFF , 1750px 470px #FFF , 725px 801px #FFF , 775px 265px #FFF , 669px 468px #FFF , 643px 1473px #FFF , 1788px 1214px #FFF , 1987px 1832px #FFF , 925px 1839px #FFF , 620px 1324px #FFF , 1776px 257px #FFF , 1572px 302px #FFF , 508px 22px #FFF , 413px 1590px #FFF , 302px 217px #FFF , 432px 1956px #FFF , 499px 639px #FFF , 1827px 1248px #FFF , 726px 1667px #FFF , 873px 1204px #FFF , 1635px 1752px #FFF , 672px 850px #FFF , 1370px 1699px #FFF , 1313px 1458px #FFF , 1839px 1899px #FFF , 1813px 1794px #FFF , 429px 1628px #FFF , 1896px 1792px #FFF , 1952px 1988px #FFF , 685px 61px #FFF , 1456px 1311px #FFF , 47px 331px #FFF , 1225px 1121px #FFF , 1309px 1579px #FFF , 1887px 617px #FFF , 250px 875px #FFF , 330px 748px #FFF , 1902px 1729px #FFF , 616px 80px #FFF , 830px 1545px #FFF , 1804px 1979px #FFF , 1170px 243px #FFF , 1760px 1060px #FFF , 117px 1181px #FFF , 1424px 762px #FFF , 998px 197px #FFF , 1661px 1778px #FFF , 395px 1770px #FFF , 1195px 193px #FFF , 1927px 303px #FFF , 721px 1640px #FFF , 941px 207px #FFF , 1026px 1986px #FFF , 729px 1812px #FFF , 119px 836px #FFF , 739px 1179px #FFF , 1940px 847px #FFF , 1230px 1109px #FFF , 893px 116px #FFF , 934px 735px #FFF , 19px 718px #FFF , 43px 206px #FFF , 1002px 27px #FFF , 1966px 1639px #FFF , 160px 1087px #FFF , 1227px 1614px #FFF , 1705px 1037px #FFF , 422px 1893px #FFF , 1737px 679px #FFF , 1482px 74px #FFF , 151px 226px #FFF , 1280px 1901px #FFF , 1795px 703px #FFF , 1446px 373px #FFF , 1380px 29px #FFF , 1851px 1890px #FFF , 816px 1296px #FFF , 942px 1985px #FFF , 1994px 446px #FFF , 183px 1746px #FFF , 1747px 1294px #FFF , 1760px 953px #FFF , 1174px 1623px #FFF , 144px 21px #FFF , 1980px 593px #FFF , 1714px 1610px #FFF , 1284px 899px #FFF , 1285px 1418px #FFF , 96px 170px #FFF , 177px 1654px #FFF , 1969px 123px #FFF , 1168px 1351px #FFF , 996px 726px #FFF , 351px 982px #FFF , 1607px 967px #FFF , 1702px 1847px #FFF , 611px 1752px #FFF , 1327px 899px #FFF , 1101px 141px #FFF , 876px 1558px #FFF , 1408px 409px #FFF;
}

#stars3 {
  width: 3px;
  height: 3px;
  background: transparent;
  box-shadow: 732px 1176px #FFF , 456px 789px #FFF , 1419px 1953px #FFF , 1678px 1457px #FFF , 1487px 1475px #FFF , 1558px 1624px #FFF , 62px 1198px #FFF , 1791px 331px #FFF , 604px 687px #FFF , 1411px 928px #FFF , 574px 1744px #FFF , 206px 115px #FFF , 325px 490px #FFF , 1365px 1610px #FFF , 118px 605px #FFF , 418px 1452px #FFF , 1869px 418px #FFF , 169px 1602px #FFF , 213px 1410px #FFF , 362px 1960px #FFF , 1520px 1712px #FFF , 1044px 1954px #FFF , 640px 1372px #FFF , 1887px 323px #FFF , 156px 1177px #FFF , 229px 984px #FFF , 1560px 496px #FFF , 665px 1634px #FFF , 285px 651px #FFF , 89px 1000px #FFF , 167px 1071px #FFF , 1374px 1184px #FFF , 232px 1921px #FFF , 384px 826px #FFF , 951px 944px #FFF , 1635px 1244px #FFF , 1374px 602px #FFF , 1888px 119px #FFF , 1523px 1216px #FFF , 1890px 1237px #FFF , 634px 1763px #FFF , 102px 94px #FFF , 1559px 1050px #FFF , 65px 672px #FFF , 258px 968px #FFF , 99px 1468px #FFF , 1497px 322px #FFF , 1604px 1417px #FFF , 1376px 182px #FFF , 1413px 1157px #FFF , 1484px 1850px #FFF , 1301px 1157px #FFF , 1023px 474px #FFF , 1478px 550px #FFF , 1940px 143px #FFF , 30px 748px #FFF , 1651px 1957px #FFF , 437px 1277px #FFF , 1870px 19px #FFF , 1194px 45px #FFF , 768px 482px #FFF , 1330px 1848px #FFF , 1343px 282px #FFF , 442px 1175px #FFF , 982px 1184px #FFF , 1165px 1473px #FFF , 1950px 400px #FFF , 1755px 1462px #FFF , 116px 959px #FFF , 538px 317px #FFF , 478px 837px #FFF , 1824px 267px #FFF , 73px 1853px #FFF , 1746px 642px #FFF , 1394px 1923px #FFF , 104px 877px #FFF , 508px 1069px #FFF , 155px 1891px #FFF , 558px 1249px #FFF , 586px 803px #FFF , 822px 1694px #FFF , 200px 890px #FFF , 1908px 1448px #FFF , 1221px 1402px #FFF , 1483px 676px #FFF , 1463px 1240px #FFF , 398px 170px #FFF , 1549px 1521px #FFF , 852px 761px #FFF , 1948px 1589px #FFF , 1191px 312px #FFF , 1665px 446px #FFF , 914px 1239px #FFF , 903px 980px #FFF , 693px 306px #FFF , 1978px 1655px #FFF , 1002px 835px #FFF , 1909px 1852px #FFF , 1441px 1553px #FFF , 726px 1136px #FFF;
  animation: animStar 150s linear infinite;
}
#stars3:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 3px;
  height: 3px;
  background: transparent;
  box-shadow: 732px 1176px #FFF , 456px 789px #FFF , 1419px 1953px #FFF , 1678px 1457px #FFF , 1487px 1475px #FFF , 1558px 1624px #FFF , 62px 1198px #FFF , 1791px 331px #FFF , 604px 687px #FFF , 1411px 928px #FFF , 574px 1744px #FFF , 206px 115px #FFF , 325px 490px #FFF , 1365px 1610px #FFF , 118px 605px #FFF , 418px 1452px #FFF , 1869px 418px #FFF , 169px 1602px #FFF , 213px 1410px #FFF , 362px 1960px #FFF , 1520px 1712px #FFF , 1044px 1954px #FFF , 640px 1372px #FFF , 1887px 323px #FFF , 156px 1177px #FFF , 229px 984px #FFF , 1560px 496px #FFF , 665px 1634px #FFF , 285px 651px #FFF , 89px 1000px #FFF , 167px 1071px #FFF , 1374px 1184px #FFF , 232px 1921px #FFF , 384px 826px #FFF , 951px 944px #FFF , 1635px 1244px #FFF , 1374px 602px #FFF , 1888px 119px #FFF , 1523px 1216px #FFF , 1890px 1237px #FFF , 634px 1763px #FFF , 102px 94px #FFF , 1559px 1050px #FFF , 65px 672px #FFF , 258px 968px #FFF , 99px 1468px #FFF , 1497px 322px #FFF , 1604px 1417px #FFF , 1376px 182px #FFF , 1413px 1157px #FFF , 1484px 1850px #FFF , 1301px 1157px #FFF , 1023px 474px #FFF , 1478px 550px #FFF , 1940px 143px #FFF , 30px 748px #FFF , 1651px 1957px #FFF , 437px 1277px #FFF , 1870px 19px #FFF , 1194px 45px #FFF , 768px 482px #FFF , 1330px 1848px #FFF , 1343px 282px #FFF , 442px 1175px #FFF , 982px 1184px #FFF , 1165px 1473px #FFF , 1950px 400px #FFF , 1755px 1462px #FFF , 116px 959px #FFF , 538px 317px #FFF , 478px 837px #FFF , 1824px 267px #FFF , 73px 1853px #FFF , 1746px 642px #FFF , 1394px 1923px #FFF , 104px 877px #FFF , 508px 1069px #FFF , 155px 1891px #FFF , 558px 1249px #FFF , 586px 803px #FFF , 822px 1694px #FFF , 200px 890px #FFF , 1908px 1448px #FFF , 1221px 1402px #FFF , 1483px 676px #FFF , 1463px 1240px #FFF , 398px 170px #FFF , 1549px 1521px #FFF , 852px 761px #FFF , 1948px 1589px #FFF , 1191px 312px #FFF , 1665px 446px #FFF , 914px 1239px #FFF , 903px 980px #FFF , 693px 306px #FFF , 1978px 1655px #FFF , 1002px 835px #FFF , 1909px 1852px #FFF , 1441px 1553px #FFF , 726px 1136px #FFF;
}

#title {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  color: #FFF;
  text-align: center;
  font-family: "lato", sans-serif;
  font-weight: 300;
  font-size: 50px;
  letter-spacing: 10px;
  margin-top: -60px;
  padding-left: 10px;
}
#title span {
  background: -webkit-linear-gradient(white, #38495a);
  -webkit-background-clip: text;
  background-clip:text;
  -webkit-text-fill-color: transparent;
}

@keyframes animStar {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-2000px);
  }
}

    html {
  display: grid;
  min-height: 100%;
}

body {
  display: grid;
  overflow: hidden;
  font-family: "Lato", sans-serif;
  text-transform: uppercase;
  text-align: center;
}

#container {
    z-index: 999;
  position: absolute;
  margin: auto;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

h1 {
  font-size: 0.9em;
  font-weight: 100;
  letter-spacing: 3px;
  padding-top: 5px;
  color: #fcfcfc;
  padding-bottom: 5px;
  text-transform: uppercase;
}

.green {
  color: #4ec07d;
}

.red {
  color: #e96075;
}

.alert {
  font-weight: 700;
  letter-spacing: 5px;
}

p {
  margin-top: -5px;
  font-size: 0.5em;
  font-weight: 100;
  color: #5e5e5e;
  letter-spacing: 1px;
}

button,
.dot {
  cursor: pointer;
}

#success-box {
  position: absolute;
  width: 35%;
  height: 100%;
  left: 12%;
  background: linear-gradient(to bottom right, #b0db7d 40%, #99dbb4 100%);
  border-radius: 20px;
  box-shadow: 5px 5px 20px rgba(203, 205, 211, 0.1);
  perspective: 40px;
}

#error-box {
    cursor:pointer;
  position: absolute;
  width: 245px;
  height: 250px;
  left: 40%;
  bottom: 35%;
  text-align: center;
  text-align: -webkit-center;
  background: linear-gradient(to bottom left, #ef8d9c 40%, #ffc39e 100%);
  border-radius: 20px;
  box-shadow: 5px 5px 20px rgba(203, 205, 211, 0.1);
}

.dot {
  width: 8px;
  height: 8px;
  background: #fcfcfc;
  border-radius: 50%;
  position: absolute;
  top: 4%;
  right: 6%;
}
.dot:hover {
  background: #c9c9c9;
}

.two {
  right: 12%;
  opacity: 0.5;
}

.face {
  position: absolute;
  width: 22%;
  height: 22%;
  background: #fcfcfc;
  border-radius: 50%;
  border: 1px solid #777777;
  top: 21%;
  left: 37.5%;
  z-index: 2;
  animation: bounce 1s ease-in infinite;
}

.face2 {
  position: absolute;
  width: 22%;
  height: 22%;
  background: #fcfcfc;
  border-radius: 50%;
  border: 1px solid #777777;
  top: 21%;
  left: 37.5%;
  z-index: 2;
  animation: roll 3s ease-in-out infinite;
}

.eye {
  position: absolute;
  width: 5px;
  height: 5px;
  background: #777777;
  border-radius: 50%;
  top: 40%;
  left: 20%;
}

.right {
  left: 68%;
}

.mouth {
  position: absolute;
  top: 43%;
  left: 41%;
  width: 7px;
  height: 7px;
  border-radius: 50%;
}

.happy {
  border: 2px solid;
  border-color: transparent #777777 #777777 transparent;
  transform: rotate(45deg);
}

.sad {
  top: 49%;
  border: 2px solid;
  border-color: #777777 transparent transparent #777777;
  transform: rotate(45deg);
}

.shadow {
  position: absolute;
  width: 21%;
  height: 3%;
  opacity: 0.5;
  background: #777777;
  left: 40%;
  top: 43%;
  border-radius: 50%;
  z-index: 1;
}

.scale {
  animation: scale 1s ease-in infinite;
}

.move {
  animation: move 3s ease-in-out infinite;
}

.message {
  position: absolute;
  width: 100%;
  text-align: center;
  height: 90%;
  top: 40%;
  font-size: larger;
}

.button-box {
  position: absolute;
  background: #fcfcfc;
  width: 50%;
  height: 15%;
  border-radius: 20px;
  top: 73%;
  left: 25%;
  outline: 0;
  border: none;
  box-shadow: 2px 2px 10px rgba(119, 119, 119, 0.5);
  transition: all 0.5s ease-in-out;
}
.button-box:hover {
  background: #efefef;
  transform: scale(1.05);
  transition: all 0.3s ease-in-out;
}

@keyframes bounce {
  50% {
    transform: translateY(-10px);
  }
}
@keyframes scale {
  50% {
    transform: scale(0.9);
  }
}
@keyframes roll {
  0% {
    transform: rotate(0deg);
    left: 25%;
  }
  50% {
    left: 60%;
    transform: rotate(168deg);
  }
  100% {
    transform: rotate(0deg);
    left: 25%;
  }
}
@keyframes move {
  0% {
    left: 25%;
  }
  50% {
    left: 60%;
  }
  100% {
    left: 25%;
  }
}
footer {
  position: absolute;
  bottom: 0;
  right: 0;
  text-align: center;
  font-size: 1em;
  text-transform: uppercase;
  padding: 10px;
  font-family: "Lato", sans-serif;
}
footer p {
  color: #ef8d9c;
  letter-spacing: 2px;
}
footer a {
  color: #b0db7d;
  text-decoration: none;
}
footer a:hover {
  color: #ffc39e;
}
</style>
<body>
    <link href='https://fonts.googleapis.com/css?family=Lato:300,400,700' rel='stylesheet' type='text/css'>
<div id='stars'></div>
<div id='stars2'></div>
<div id='stars3'></div>

    <div id="container">
        <div id="error-box">
            <div class="dot"></div>
            <div class="dot two"></div>
            <div class="face2">
                <div class="eye"></div>
                <div class="eye right"></div>
                <div class="mouth sad"></div>
            </div>
            <div class="shadow move"></div>
            <div class="message"><h1 class="alert">Error!</h1><p><b style="font-size:larger">{{error}}</b></div>
                <button class="button-box"><h1 class="red" ><a style="text-decoration: none;color: #090a0f;" id="tryagain">try again</a></h1></button>
            </div>
        </div>


      <footer>
        <p>BPlanV2 ♡
      </footer>
<script>
  document.getElementById("tryagain").onclick = function(){
    window.location.href = window.location.origin;
  }
    dragElement(document.getElementById("error-box"));

function dragElement(elmnt) {
  var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
  if (document.getElementById(elmnt.id + "header")) {
    // if present, the header is where you move the DIV from:
    document.getElementById(elmnt.id + "header").onmousedown = dragMouseDown;
  } else {
    // otherwise, move the DIV from anywhere inside the DIV:
    elmnt.onmousedown = dragMouseDown;
  }

  function dragMouseDown(e) {
    e = e || window.event;
    e.preventDefault();
    // get the mouse cursor position at startup:
    pos3 = e.clientX;
    pos4 = e.clientY;
    document.onmouseup = closeDragElement;
    // call a function whenever the cursor moves:
    document.onmousemove = elementDrag;
  }

  function elementDrag(e) {
    e = e || window.event;
    e.preventDefault();
    // calculate the new cursor position:
    pos1 = pos3 - e.clientX;
    pos2 = pos4 - e.clientY;
    pos3 = e.clientX;
    pos4 = e.clientY;
    // set the element's new position:
    elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
    elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
  }

  function closeDragElement() {
    // stop moving when mouse button is released:
    document.onmouseup = null;
    document.onmousemove = null;
  }
}
</script>
<script defer>
  setTimeout(() => {
    window.location.reload()
  }, 1100);
</script>
    </body>
</html>