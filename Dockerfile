# Use an official Python runtime as a base image
FROM python:3.9-slim
# Use an official lightweight Python image
# FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

# Set working directory
WORKDIR /app

# Install system dependencies (if needed)
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first (for better caching)
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip && pip install -r requirements.txt

# Copy project files
COPY . .

# Expose Sanic’s default port
EXPOSE 80

# Run app.py when the container launches
CMD ["python", "-m", "granian", "--host","0.0.0.0","--port 80","--interface","asgi","index:app"]