    .forehide{
        display: none;
    }

    html,
    body {
        height: 100%;
        margin: 0;
    }
    @keyframes appeared{
        to {
            opacity: 1;
        }
    }
    #header {
        top: 0;
        left: 0;
        right: 0;
        position: sticky;
        flex: 0 0 100%;
        align-self: flex-start; 
    }
    #header h3{
        flex-basis: 10%;
    }
    #header h3:nth-child(1){
        flex-basis: 70%;
    }
    .form-check-input[type=radio] {
        width: 2.5em !important;
        height: 2.5em !important;
    }
    @keyframes smooth-appear {
        to {
            bottom: 20px;
            opacity: 1;
            top: 50%;
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
        }
    }    
    * {
        transition: all 0.6s ease;
    }
    .appear {
        opacity: 0 ;
        animation: appeared 1.5s ease forwards;
    }
    .intro {
        margin-top: auto;
        margin-right: auto;
    }
    
    .container {
        height: 100%;
        bottom: -100%;
        animation: smooth-appear 1.5s ease forwards;
        /* margin: 0 auto; */
        text-align: center;
        position: relative;
        opacity: 0;
        /* top: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%); */
        /* width: 50%; */
        /* border: 3px solid green; */
        padding: 10px;
        width: 100%;
        height: 96%;
        border-radius: 15px;
        color: white;
        background-color: rgba(0, 0, 0, 0.20);
    }

    .background {
        overflow: hidden;
        width: 100%;
        height: 100%;
        position: fixed;
        z-index: -1;
        background-image: url(/static/img/bg1.jpg);
        background-repeat: no-repeat;
        background-size: cover;
        filter: blur(3px);
        -webkit-filter: blur(3px);
        margin: 0;
        padding: 0;
        top: 0;
        left: 0;
        background-position: center;
    }
    .flexible {
        font-size: xx-small;
    }

    .flexible {
        text-align: left;
        padding-left:15px !important;
        margin-bottom: 5px !important;
        display: flex;
        flex-wrap: wrap;
        list-style: none;
        padding-left: 0;
    }
    .flexible hr{
        flex: 0 0 100%;
    }
    .flexible #header{
        flex: 0 0 100%;
    }
    .flexible #header *:nth-of-type(1){
        flex-basis: 70%;
    }
    .flexible #header *{
        flex-basis: 10%;
    }
    .flexible ul h6{
        flex-basis: 70%;
    }
    .flexible ul li{
        flex-basis: 10%;
    }

    .flexible ul{
        flex: 0 0 100%;
    }
    .flexible *{
        flex: 0 0 25%;
    }
    .mainform{
        margin: auto;width: 100%;display: inline-flex;
    }
    .suform{
        margin: auto;
    }
