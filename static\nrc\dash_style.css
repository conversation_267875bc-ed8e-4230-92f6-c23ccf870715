    .ncp {
        -webkit-user-select: none; /* Safari */
        -khtml-user-select: none; /* Konqueror HTML */
        -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* Internet Explorer/Edge */
        user-select: none; /* Standard syntax */
    }
    html,body{
        height: 100%;
    }
    .groupclicker{
        cursor: pointer;
    }
    .container{
        height: 100%;
    }
    @keyframes smooth-appear {
        to {
            opacity: 1;
        }
    }

    #maingroup {
        position: relative;
        animation: smooth-appear 1.5s forwards!important;
        transition: all 0.5s ease;
        /* animation: ease 1s forwards; */

    }
    .showsup{
        animation: smooth-appear 1.5s forwards!important;

    }
    .intro{
        opacity: 0;
    }
    #maingroup > div {
        height: 100% !important;
    }
    #maincons {
        transition: all 1s ease;
    }

    .groupbut {
        width: fit-content;font-size: xx-large;padding: 3px 20px 3px 20px;margin: 10px;
    }
    .active_core{
        color: var(--bs-btn-active-color);
        background-color: lightgreen;
        border-color: var(--bs-btn-active-border-color);
    }
    ::placeholder {
      color: gray !important;
      opacity: 1; /* Firefox */
    }
    
    ::-ms-input-placeholder { /* Edge 12-18 */
      color: red;
    }

    .flexible {
        font-size: small;
        white-space:nowrap;

        overflow: auto;
    
    }

    .flexible {
        text-align: left;
        padding-left:0 !important;
        margin-bottom: 5px !important;
        display: flex;
        flex-wrap: wrap;
        list-style: none;
        padding-left: 0;
    }
    .flexible hr{
        flex: 0 0 100%;
    }
    .flexible #header{
        flex: 0 0 100%;
    }
    .flexible #header *:nth-of-type(1){
        flex-basis: 70%;
    }
    .flexible #header *{
        flex-basis: 15%;
    }
    .flexible ul h6{
        flex-basis: 70%;
    }
    .flexible ul li{
        flex-basis: 15%;
    }
    .flexible ul li:nth-child(4){
        flex-basis: 70%;
    }
    .flexible ul{
        flex: 0 0 100%;
    }
    .flexible *{
        flex: 0 0 25%;
    }
    .rwd-table {
        margin: 1em 0;
        max-width: 100%;
  /* min-width: 300px; */
}
.rwd-table tr td{
    height: 1vh;

}
.rwd-table tr {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}
.rwd-table th {
  display: none;
}
.rwd-table td {
  display: block;
}
.rwd-table td:first-child {
  padding-top: .5em;
}
.rwd-table td:last-child {
  padding-bottom: .5em;
}
.rwd-table td:before {
  content: attr(data-th) ": ";
  font-weight: bold;
  /* width: 6.5em; */
  display: inline-block;
}
@media (min-width: 1px) {
  .rwd-table td:before {
    display: none;
  }
}
.rwd-table th, .rwd-table td {
  text-align: center;
}
@media (min-width: 1px) {
  .rwd-table th, .rwd-table td {
    display: table-cell;
    /* padding: .25em .5em; */
    border: aqua 1px solid;
    border-radius: 5px;
  }
  .rwd-table th:first-child, .rwd-table td:first-child {
    padding-left: 0;
  }
  .rwd-table th:last-child, .rwd-table td:last-child {
    padding-right: 0;
  }
}

body {
  padding: 3px;
  font-family: Montserrat, sans-serif;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  color: #444;
  background: #eee;
  width: 100%;
  height: 100%;
  margin: 0px;
  padding: 0px;
}

h1 {
  font-weight: normal;
  letter-spacing: -1px;
  color: #34495E;
}

.rwd-table {
  background: #34495E;
  color: #fff;
  border-radius: .4em;
  overflow: auto;
}
.rwd-table tr {
  border-color: #46637f;
}
.rwd-table th, .rwd-table td {
  margin: 2px;
}
@media (min-width: 1px) {
  .rwd-table th, .rwd-table td {
    padding: 3px !important;
  }
}
.rwd-table th, .rwd-table td:before {
  color: #dd5;
}
    html,
    body {
        height: 100%;
        margin: 0;
        min-height: 100% !important;
    }
    @keyframes appeared{
        to {
            opacity: 1;
        }
    }
    #header {
        top: 0;
        left: 0;
        right: 0;
        position: sticky;
        flex: 0 0 100%;
        align-self: flex-start; 
    }
    #header h3{
        flex-basis: 15%;
    }
    #header h3:nth-child(1){
        flex-basis: 70%;
    }
    .form-check-input[type=checkbox] {
        width: 1.5em !important;
        height: 1.5em !important;
    }
    .chat {
        margin-top: auto;
        margin-bottom: auto;
    }
    
    .card {
        height: 100%;
        width: 100%;
        border-radius: 15px !important;
        background-color: rgba(0, 0, 0, 0.4) !important;
    }

    .contacts_body {
        padding: 0.75rem 0 !important;
        overflow-y: auto;
        white-space: nowrap;
    }

    .msg_card_body {
        overflow-y: auto;
    }
    

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 0 !important;
    }

    .card-footer {
        border-radius: 0 0 15px 15px !important;
        border-top: 0 !important;
    }

    .container {
        align-content: center;
    }

    .search {
        border-radius: 15px 0 0 15px !important;
        background-color: rgba(0, 0, 0, 0.3) !important;
        border: 0 !important;
        color: white !important;
    }

    .search:focus {
        box-shadow: none !important;
        outline: 0px !important;
    }

    .type_msg {
        background-color: rgba(0, 0, 0, 0.3) !important;
        border: 0 !important;
        color: white !important;
        height: 60px !important;
        overflow-y: auto;
    }

    .type_msg:focus {
        box-shadow: none !important;
        outline: 0px !important;
    }
    .input-group-append{
        width: 50%;
    }
    .input-group-text{

        font-size: x-large;
    }
    .attach_btn {
        border-radius: 15px 0 0 15px !important;
        background-color: rgba(0, 0, 0, 0.3) !important;
        border: 0 !important;
        color: white !important;
        cursor: pointer;
    }
    .mid_btn {
        border-radius: 0 !important;
        background-color: rgba(0, 0, 0, 0.3) !important;
        border: 0 !important;
        color: white !important;
        cursor: pointer;
    }
    .send_btn {
        border-radius: 0 15px 15px 0 !important;
        background-color: rgba(0, 0, 0, 0.3) !important;
        border: 0 !important;
        color: white !important;
        cursor: pointer;
    }

    .search_btn {
        border-radius: 0 15px 15px 0 !important;
        background-color: rgba(0, 0, 0, 0.3) !important;
        border: 0 !important;
        color: white !important;
        cursor: pointer;
    }

    .contacts {
        list-style: none;
        padding: 0;
    }

    .contacts li {
        width: 100% !important;
        padding: 5px 10px;
        margin-bottom: 15px !important;
    }

    .active {
        background-color: rgba(0, 0, 0, 0.3);
    }

    .user_img {
        height: 70px;
        width: 70px;
        border: 1.5px solid #f5f6fa;

    }

    .user_img_msg {
        height: 40px;
        width: 40px;
        border: 1.5px solid #f5f6fa;

    }

    .img_cont {
        position: relative;
        height: 70px;
        width: 70px;
    }

    .img_cont_msg {
        height: 40px;
        width: 40px;
    }

    .online_icon {
        position: absolute;
        height: 15px;
        width: 15px;
        background-color: #4cd137;
        border-radius: 50%;
        bottom: 0.2em;
        right: 0.4em;
        border: 1.5px solid white;
    }

    .offline {
        background-color: #c23616 !important;
    }

    .user_info {
        margin-top: auto;
        margin-bottom: auto;
        margin-left: 15px;
    }

    .user_info span {
        font-size: 20px;
        color: white;
    }

    .user_info p {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
    }

    .video_cam {
        margin-left: auto;
        margin-bottom: auto;
    }

    .video_cam span {
        color: white;
        font-size: 20px;
        cursor: pointer;
        margin-right: 20px;
    }

    .msg_cotainer {
        margin-top: auto;
        margin-bottom: auto;
        margin-left: 10px;
        border-radius: 25px;
        background-color: #82ccdd;
        padding: 10px;
        position: relative;
    }

    .msg_cotainer_send {
        margin-top: auto;
        margin-bottom: auto;
        margin-right: 10px;
        border-radius: 25px;
        background-color: #78e08f;
        padding: 10px;
        position: relative;
    }

    .msg_time {
        position: absolute;
        left: 0;
        bottom: -15px;
        color: rgba(255, 255, 255, 0.5);
        font-size: 10px;
    }

    @media(max-width: 576px){
        .contacts_card{
            margin-bottom: 15px !important;
        }
	}

    .msg_time_send {
        position: absolute;
        right: 0;
        bottom: -15px;
        color: rgba(255, 255, 255, 0.5);
        font-size: 10px;
    }

    .msg_head {
        position: relative;
    }

    #action_menu_btn {
        position: absolute;
        right: 10px;
        top: 10px;
        color: white;
        cursor: pointer;
        font-size: 20px;
    }

    .action_menu {
        z-index: 1;
        position: absolute;
        padding: 15px 0;
        background-color: rgba(0, 0, 0, 1);
        color: white;
        border-radius: 15px;
        top: 30px;
        right: 15px;
        display: none;
    }

    .action_menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .action_menu ul li {
        width: 100%;
        padding: 10px 15px;
        margin-bottom: 5px;
    }

    .action_menu ul li i {
        padding-right: 10px;

    }

    .action_menu ul li:hover {
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.2);
    }

    .background {
        width: 100%;
        height: 100%;
        position: fixed;
        z-index: -1;
        background-image: url(/static/img/bg1.jpg);
        background-repeat: no-repeat;
        background-size: cover;
        filter: blur(3px);
        -webkit-filter: blur(3px);
        margin: 0;
        padding: 0;
        top: 0 !important;
        right:0 !important;
        background-position: center;
    }

    .container {
        padding: 5px;
        width: 100%;
        height: 97%;
    }