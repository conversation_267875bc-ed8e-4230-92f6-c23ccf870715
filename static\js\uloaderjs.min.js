function finishload(){document.getElementById("uloadermain").classList.remove("uloader-viewex"),document.getElementById("uloadermain").classList.add("uloader-hiddex")}var node_1=document.createElement("DIV");node_1.setAttribute("id","uloadermain"),node_1.setAttribute("class","uloader-mainpage uloader-hiddex");var node_2=document.createElement("DIV");node_2.setAttribute("class","uloader-content"),node_1.appendChild(node_2);var node_3=document.createElement("CENTER");node_2.appendChild(node_3);var node_4=document.createElement("H2");node_4.setAttribute("style","color: white;"),node_3.appendChild(node_4);var node_5=document.createTextNode(new String("Loading"));node_4.appendChild(node_5);var node_6=document.createElement("DIV");node_6.setAttribute("class","uloader-loadingspinner"),node_3.appendChild(node_6);var node_7=document.createElement("DIV");node_7.setAttribute("id","square1"),node_6.appendChild(node_7);var node_8=document.createElement("DIV");node_8.setAttribute("id","square2"),node_6.appendChild(node_8);var node_9=document.createElement("DIV");node_9.setAttribute("id","square3"),node_6.appendChild(node_9);var node_10=document.createElement("DIV");node_10.setAttribute("id","square4"),node_6.appendChild(node_10);var node_11=document.createElement("DIV");node_11.setAttribute("id","square5"),node_6.appendChild(node_11),document.body.appendChild(node_1),css=".uloader-hiddex {visibility: hidden !important;opacity: 0 !important;transition: visibility 0.7s linear ,opacity 0.7s linear;}.uloader-viewex {visibility: visible !important;opacity: 1 !important;}.uloader-mainpage {position: fixed;top: 0;z-index: 999;top: 0;left: 0;right: 0;bottom: 0;width: 100%;height: 100%;opacity: 1;background-color: black;}.uloader-content {top: 35%;position: relative;}.uloader-loadingspinner {--square: 26px;--offset: 30px;--duration: 2.4s;--delay: 0.2s;--timing-function: ease-in-out;--in-duration: 0.4s;--in-delay: 0.1s;--in-timing-function: ease-out;width: calc( 3 * var(--offset) + var(--square));height: calc( 2 * var(--offset) + var(--square));padding: 0px;margin-left: auto;margin-right: auto;margin-top: 10px;margin-bottom: 30px;position: relative;}.uloader-loadingspinner div {display: inline-block;background: darkorange;border: none;border-radius: 2px;width: var(--square);height: var(--square);position: absolute;padding: 0px;margin: 0px;font-size: 6pt;color: black;}.uloader-loadingspinner #square1 {left: calc( 0 * var(--offset) );top: calc( 0 * var(--offset) );animation: square1 var(--duration) var(--delay) var(--timing-function) infinite, squarefadein var(--in-duration) calc(1 * var(--in-delay)) var(--in-timing-function) both;}.uloader-loadingspinner #square2 {left: calc( 0 * var(--offset) );top: calc( 1 * var(--offset) );animation: square2 var(--duration) var(--delay) var(--timing-function) infinite, squarefadein var(--in-duration) calc(1 * var(--in-delay)) var(--in-timing-function) both;}.uloader-loadingspinner #square3 {left: calc( 1 * var(--offset) );top: calc( 1 * var(--offset) );animation: square3 var(--duration) var(--delay) var(--timing-function) infinite, squarefadein var(--in-duration) calc(2 * var(--in-delay)) var(--in-timing-function) both;}.uloader-loadingspinner #square4 {left: calc( 2 * var(--offset) );top: calc( 1 * var(--offset) );animation: square4 var(--duration) var(--delay) var(--timing-function) infinite, squarefadein var(--in-duration) calc(3 * var(--in-delay)) var(--in-timing-function) both;}.uloader-loadingspinner #square5 {left: calc( 3 * var(--offset) );top: calc( 1 * var(--offset) );animation: square5 var(--duration) var(--delay) var(--timing-function) infinite, squarefadein var(--in-duration) calc(4 * var(--in-delay)) var(--in-timing-function) both;}@keyframes square1 {0% {left: calc( 0 * var(--offset) );top: calc( 0 * var(--offset) );}8.333% {left: calc( 0 * var(--offset) );top: calc( 1 * var(--offset) );}100% {left: calc( 0 * var(--offset) );top: calc( 1 * var(--offset) );}}@keyframes square2 {0% {left: calc( 0 * var(--offset) );top: calc( 1 * var(--offset) );}8.333% {left: calc( 0 * var(--offset) );top: calc( 2 * var(--offset) );}16.67% {left: calc( 1 * var(--offset) );top: calc( 2 * var(--offset) );}25.00% {left: calc( 1 * var(--offset) );top: calc( 1 * var(--offset) );}83.33% {left: calc( 1 * var(--offset) );top: calc( 1 * var(--offset) );}91.67% {left: calc( 1 * var(--offset) );top: calc( 0 * var(--offset) );}100% {left: calc( 0 * var(--offset) );top: calc( 0 * var(--offset) );}}@keyframes square3 {0%,100% {left: calc( 1 * var(--offset) );top: calc( 1 * var(--offset) );}16.67% {left: calc( 1 * var(--offset) );top: calc( 1 * var(--offset) );}25.00% {left: calc( 1 * var(--offset) );top: calc( 0 * var(--offset) );}33.33% {left: calc( 2 * var(--offset) );top: calc( 0 * var(--offset) );}41.67% {left: calc( 2 * var(--offset) );top: calc( 1 * var(--offset) );}66.67% {left: calc( 2 * var(--offset) );top: calc( 1 * var(--offset) );}75.00% {left: calc( 2 * var(--offset) );top: calc( 2 * var(--offset) );}83.33% {left: calc( 1 * var(--offset) );top: calc( 2 * var(--offset) );}91.67% {left: calc( 1 * var(--offset) );top: calc( 1 * var(--offset) );}}@keyframes square4 {0% {left: calc( 2 * var(--offset) );top: calc( 1 * var(--offset) );}33.33% {left: calc( 2 * var(--offset) );top: calc( 1 * var(--offset) );}41.67% {left: calc( 2 * var(--offset) );top: calc( 2 * var(--offset) );}50.00% {left: calc( 3 * var(--offset) );top: calc( 2 * var(--offset) );}58.33% {left: calc( 3 * var(--offset) );top: calc( 1 * var(--offset) );}100% {left: calc( 3 * var(--offset) );top: calc( 1 * var(--offset) );}}@keyframes square5 {0% {left: calc( 3 * var(--offset) );top: calc( 1 * var(--offset) );}50.00% {left: calc( 3 * var(--offset) );top: calc( 1 * var(--offset) );}58.33% {left: calc( 3 * var(--offset) );top: calc( 0 * var(--offset) );}66.67% {left: calc( 2 * var(--offset) );top: calc( 0 * var(--offset) );}75.00% {left: calc( 2 * var(--offset) );top: calc( 1 * var(--offset) );}100% {left: calc( 2 * var(--offset) );top: calc( 1 * var(--offset) );}}@keyframes squarefadein {0% {transform: scale(0.75);opacity: 0.0;}100% {transform: scale(1.0);opacity: 1.0;}}";var styleSheet=document.createElement("style");document.getElementById("uloadermain").classList.remove("uloader-hiddex"),document.getElementById("uloadermain").classList.add("uloader-viewex"),styleSheet.textContent=css,document.head.appendChild(styleSheet),window.onload=finishload;